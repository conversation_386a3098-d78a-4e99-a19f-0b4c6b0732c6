# RAG Project: Full-Stack Chat Application with Intelligent Document Processing

This project provides a complete RAG (Retrieval Augmented Generation) solution with a modern chat interface. It combines a TypeScript backend with Langchain and ChromaDB for document processing and retrieval, and a Next.js frontend with a beautiful chat interface built using Tailwind CSS and shadcn/ui components.

## Features

- **🚀 Full-Stack Solution**: Complete chat application with backend API and modern frontend
- **💬 Intelligent Chat Interface**: Beautiful, responsive chat UI with conversation history
- **📄 Multi-Format Document Processing**: Supports `.txt`, `.docx`, and `.pdf` files
- **🧠 Smart Question Reformulation**: Uses conversation history to improve document retrieval
- **🌍 Multi-Language Support**: Responds in the same language as the user's question
- **🔍 Advanced RAG Pipeline**: Combines document retrieval with Google Gemini for intelligent responses
- **🐳 Dockerized Environment**: Easy deployment with Docker Compose
- **⚡ Real-time Chat**: Instant responses with loading indicators and error handling
- **🎨 Modern UI**: Built with Next.js, Tailwind CSS, and shadcn/ui components
- **🔄 Conversation History**: Maintains context across multiple exchanges

## Project Structure

```
project-root/
├── src/
│   ├── app.ts                  # Main application entry point
│   ├── routes/                 # API route definitions
│   │   ├── process.ts          # Endpoint for file processing
│   │   ├── query.ts            # Endpoint for querying ChromaDB
│   │   └── rag.ts              # Endpoint for RAG functionality
│   ├── services/               # Business logic and external service integrations
│   │   ├── langchainService.ts # Langchain related operations (embedding, retrieval)
│   │   └── chromaService.ts    # ChromaDB direct interactions
│   └── utils/                  # Utility functions
│       └── fileProcessor.ts    # Handles text extraction from different file types
├── input_files/                # Directory for input files to be processed
├── docker-compose.yml          # Docker Compose configuration for services
├── Dockerfile                  # Dockerfile for the TypeScript application
├── tsconfig.json               # TypeScript compiler configuration
├── package.json                # Project dependencies and scripts
└── README.md                   # Project documentation
```

## Setup and Installation

1.  **Clone the repository:**

    ```bash
    git clone <repository_url>
    cd <repository_name>
    ```

2.  **Environment Variables:**

    Create a `.env` file in the `project-root` directory and add your OpenAI API key:

    ```
    OPENAI_API_KEY=your_openai_api_key_here
    ```

3.  **Build and Run with Docker Compose:**

    ```bash
    docker-compose up --build
    ```

    This will:

    - Build the TypeScript application Docker image.
    - Start the ChromaDB service.
    - Start the TypeScript application.

    The application will be accessible at `http://localhost:3000`.

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Node.js 20+ (for local development)
- OpenAI API Key (for embeddings)
- Google API Key (for Gemini model)

### Environment Setup

Create a `.env` file in the root directory:

```env
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

### Running with Docker (Recommended)

1. **Start all services:**

   ```bash
   docker-compose up --build
   ```

2. **Access the application:**

   - Frontend (Chat Interface): http://localhost:3000
   - Backend API: http://localhost:5005
   - ChromaDB: http://localhost:8000

3. **Upload documents:**

   ```bash
   curl -X POST -F "file=@/path/to/your/document.pdf" http://localhost:5005/api/process
   ```

4. **Use the chat interface:**
   - Open http://localhost:3000 in your browser
   - Start chatting with your documents!

### Running for Development

1. **Start ChromaDB:**

   ```bash
   docker-compose up chroma -d
   ```

2. **Start Backend:**

   ```bash
   npm install
   npm run dev
   ```

3. **Start Frontend (in another terminal):**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

## API Endpoints

### 1. Process Files (`POST /api/process`)

Processes a file (text, Word, or PDF), extracts its content, generates embeddings, and stores them in the Chroma database.

- **Method**: `POST`
- **Endpoint**: `/api/process`
- **Content-Type**: `multipart/form-data`
- **Form Field**: `file` (the file to upload)

**Example using `curl`:**

```bash
curl -X POST -F "file=@/path/to/your/document.pdf" http://localhost:5005/api/process
```

### 2. Query ChromaDB (`GET /api/query`)

Queries the Chroma database for document chunks relevant to a given text query.

- **Method**: `GET`
- **Endpoint**: `/api/query`
- **Query Parameter**: `q` (the query string)

**Example using `curl`:**

```bash
curl "http://localhost:5005/api/query?q=What is Langchain?"
```

### 3. RAG Endpoint with History (`POST /api/rag`)

Handles user questions by retrieving relevant information from ChromaDB and generating an answer using the Gemini model. Now supports conversation history for better context understanding.

- **Method**: `POST`
- **Endpoint**: `/api/rag`
- **Content-Type**: `application/json`
- **Request Body**:
  ```json
  {
    "question": "Your question here",
    "history": [
      { "role": "user", "content": "Previous question" },
      { "role": "assistant", "content": "Previous answer" }
    ]
  }
  ```

**Example using `curl`:**

```bash
curl -X POST -H "Content-Type: application/json" -d "{\"question\": \"Tell me about the processed documents.\", \"history\": []}" http://localhost:5005/api/rag
```

## Development

To run the application in development mode with `nodemon` for auto-reloading:

```bash
# Inside the project-root directory
npm install
npm run dev
```

Remember to have ChromaDB running separately or via `docker-compose up`.

## Future Enhancements

- More robust error handling and logging.
- User authentication and authorization.
- Support for more document types and advanced parsing.
- Scalability improvements for ChromaDB and the application.
- Integration with other LLMs and embedding models.
- A simple frontend for easier interaction.
