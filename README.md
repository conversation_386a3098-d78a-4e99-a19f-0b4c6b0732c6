# RAG Project: Full-Stack Chat Application with Intelligent Document Processing

This project provides a complete RAG (Retrieval Augmented Generation) solution with a modern chat interface. It combines a TypeScript backend with Langchain and ChromaDB for document processing and retrieval, and a Next.js frontend with a beautiful chat interface built using Tailwind CSS and shadcn/ui components.

## Features

- **🚀 Full-Stack Solution**: Complete chat application with backend API and modern frontend
- **💬 Intelligent Chat Interface**: Beautiful, responsive chat UI with conversation history
- **📄 Multi-Format Document Processing**: Supports `.txt`, `.docx`, and `.pdf` files
- **🧠 Smart Question Reformulation**: Uses conversation history to improve document retrieval
- **🌍 Multi-Language Support**: Responds in the same language as the user's question
- **🔍 Advanced RAG Pipeline**: Combines document retrieval with Google Gemini for intelligent responses
- **🐳 Dockerized Environment**: Easy deployment with Docker Compose
- **⚡ Real-time Chat**: Instant responses with loading indicators and error handling
- **🎨 Modern UI**: Built with Next.js, Tailwind CSS, and shadcn/ui components
- **🔄 Conversation History**: Maintains context across multiple exchanges

## Project Structure

```
project-root/
├── src/                        # Backend source code
│   ├── app.ts                  # Main application entry point with CORS support
│   ├── routes/                 # API route definitions
│   │   ├── process.ts          # Endpoint for file processing
│   │   ├── query.ts            # Endpoint for querying ChromaDB
│   │   └── rag.ts              # Enhanced RAG endpoint with history support
│   ├── services/               # Business logic and external service integrations
│   │   ├── langchainService.ts # Langchain related operations (embedding, retrieval)
│   │   └── chromaService.ts    # ChromaDB direct interactions
│   └── utils/                  # Utility functions
│       └── fileProcessor.ts    # Handles text extraction from different file types
├── frontend/                   # Next.js frontend application
│   ├── src/
│   │   ├── app/                # Next.js app directory
│   │   │   ├── page.tsx        # Main chat page
│   │   │   ├── layout.tsx      # Root layout
│   │   │   └── globals.css     # Global styles with Tailwind
│   │   ├── components/         # React components
│   │   │   ├── ui/             # shadcn/ui components
│   │   │   └── chat/           # Chat-specific components
│   │   │       ├── ChatInterface.tsx    # Main chat component
│   │   │       ├── ChatMessage.tsx      # Message display component
│   │   │       └── ChatInput.tsx        # Message input component
│   │   ├── services/           # Frontend services
│   │   │   └── chatService.ts  # API communication service
│   │   ├── types/              # TypeScript type definitions
│   │   │   └── chat.ts         # Chat-related types
│   │   └── lib/                # Utility libraries
│   │       └── utils.ts        # shadcn/ui utilities
│   ├── package.json            # Frontend dependencies
│   ├── next.config.ts          # Next.js configuration
│   ├── tailwind.config.ts      # Tailwind CSS configuration
│   ├── components.json         # shadcn/ui configuration
│   └── Dockerfile              # Frontend Docker configuration
├── input_files/                # Directory for input files (mounted in Docker)
├── dist/                       # Compiled TypeScript output
├── package.json                # Backend Node.js project configuration
├── tsconfig.json               # TypeScript configuration
├── Dockerfile                  # Backend Docker configuration
├── docker-compose.yml          # Docker Compose with frontend and backend services
└── README.md                   # Project documentation
```

## Setup and Installation

1.  **Clone the repository:**

    ```bash
    git clone <repository_url>
    cd <repository_name>
    ```

2.  **Environment Variables:**

    Create a `.env` file in the `project-root` directory and add your OpenAI API key:

    ```
    OPENAI_API_KEY=your_openai_api_key_here
    ```

3.  **Build and Run with Docker Compose:**

    ```bash
    docker-compose up --build
    ```

    This will:

    - Build the TypeScript application Docker image.
    - Start the ChromaDB service.
    - Start the TypeScript application.

    The application will be accessible at `http://localhost:3000`.

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Node.js 20+ (for local development)
- OpenAI API Key (for embeddings)
- Google API Key (for Gemini model)

### Environment Setup

Create a `.env` file in the root directory:

```env
OPENAI_API_KEY=your_openai_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

### Running with Docker (Recommended)

1. **Start all services:**

   ```bash
   docker-compose up --build
   ```

2. **Access the application:**

   - Frontend (Chat Interface): http://localhost:3000
   - Backend API: http://localhost:5005
   - ChromaDB: http://localhost:8000

3. **Upload documents:**

   ```bash
   curl -X POST -F "file=@/path/to/your/document.pdf" http://localhost:5005/api/process
   ```

4. **Use the chat interface:**
   - Open http://localhost:3000 in your browser
   - Start chatting with your documents!

### Running for Development

1. **Start ChromaDB:**

   ```bash
   docker-compose up chroma -d
   ```

2. **Start Backend:**

   ```bash
   npm install
   npm run dev
   ```

3. **Start Frontend (in another terminal):**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

## API Endpoints

### 1. Process Files (`POST /api/process`)

Processes a file (text, Word, or PDF), extracts its content, generates embeddings, and stores them in the Chroma database.

- **Method**: `POST`
- **Endpoint**: `/api/process`
- **Content-Type**: `multipart/form-data`
- **Form Field**: `file` (the file to upload)

**Example using `curl`:**

```bash
curl -X POST -F "file=@/path/to/your/document.pdf" http://localhost:5005/api/process
```

### 2. Query ChromaDB (`GET /api/query`)

Queries the Chroma database for document chunks relevant to a given text query.

- **Method**: `GET`
- **Endpoint**: `/api/query`
- **Query Parameter**: `q` (the query string)

**Example using `curl`:**

```bash
curl "http://localhost:5005/api/query?q=What is Langchain?"
```

### 3. RAG Endpoint with History (`POST /api/rag`)

Handles user questions by retrieving relevant information from ChromaDB and generating an answer using the Gemini model. Now supports conversation history for better context understanding.

- **Method**: `POST`
- **Endpoint**: `/api/rag`
- **Content-Type**: `application/json`
- **Request Body**:
  ```json
  {
    "question": "Your question here",
    "history": [
      { "role": "user", "content": "Previous question" },
      { "role": "assistant", "content": "Previous answer" }
    ]
  }
  ```

**Example using `curl`:**

```bash
curl -X POST -H "Content-Type: application/json" -d "{\"question\": \"Tell me about the processed documents.\", \"history\": []}" http://localhost:5005/api/rag
```

## Development

To run the application in development mode with `nodemon` for auto-reloading:

```bash
# Inside the project-root directory
npm install
npm run dev
```

Remember to have ChromaDB running separately or via `docker-compose up`.

## Future Enhancements

- More robust error handling and logging.
- User authentication and authorization.
- Support for more document types and advanced parsing.
- Scalability improvements for ChromaDB and the application.
- Integration with other LLMs and embedding models.
- A simple frontend for easier interaction.
