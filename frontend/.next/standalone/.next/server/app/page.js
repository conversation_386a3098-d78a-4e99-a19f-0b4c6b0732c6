(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var n=t(1658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,r)=>{"use strict";function t(e){return e.endsWith("/route")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isAppRouteRoute",{enumerable:!0,get:function(){return t}})},660:(e,r)=>{"use strict";function t(e){let r=5381;for(let t=0;t<e.length;t++)r=(r<<5)+r+e.charCodeAt(t)|0;return r>>>0}function n(e){return t(e).toString(36).slice(0,5)}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{djb2Hash:function(){return t},hexHash:function(){return n}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},968:(e,r,t)=>{Promise.resolve().then(t.bind(t,4054))},1135:()=>{},1204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var n=t(7413),o=t(4054);function i(){return(0,n.jsx)(o.default,{})}},1437:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{INTERCEPTION_ROUTE_MARKERS:function(){return o},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return i}});let n=t(4722),o=["(..)(..)","(.)","(..)","(...)"];function i(e){return void 0!==e.split("/").find(e=>o.find(r=>e.startsWith(r)))}function a(e){let r,t,i;for(let n of e.split("/"))if(t=o.find(e=>n.startsWith(e))){[r,i]=e.split(t,2);break}if(!r||!t||!i)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(r=(0,n.normalizeAppPath)(r),t){case"(.)":i="/"===r?"/"+i:r+"/"+i;break;case"(..)":if("/"===r)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});i=r.split("/").slice(0,-1).concat(i).join("/");break;case"(...)":i="/"+i;break;case"(..)(..)":let a=r.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});i=a.slice(0,-2).concat(i).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:r,interceptedRoute:i}}},1658:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{fillMetadataSegment:function(){return p},normalizeMetadataPageToRoute:function(){return m},normalizeMetadataRoute:function(){return f}});let n=t(8304),o=function(e){return e&&e.__esModule?e:{default:e}}(t(8671)),i=t(6341),a=t(4396),s=t(660),l=t(4722),c=t(2958),u=t(5499);function d(e){let r=o.default.dirname(e);if(e.endsWith("/sitemap"))return"";let t="";return r.split("/").some(e=>(0,u.isGroupSegment)(e)||(0,u.isParallelRouteSegment)(e))&&(t=(0,s.djb2Hash)(r).toString(36).slice(0,6)),t}function p(e,r,t){let n=(0,l.normalizeAppPath)(e),s=(0,a.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),u=(0,i.interpolateDynamicPath)(n,r,s),{name:p,ext:f}=o.default.parse(t),m=d(o.default.posix.join(e,p)),h=m?`-${m}`:"";return(0,c.normalizePathSep)(o.default.join(u,`${p}${h}${f}`))}function f(e){if(!(0,n.isMetadataPage)(e))return e;let r=e,t="";if("/robots"===e?r+=".txt":"/manifest"===e?r+=".webmanifest":t=d(e),!r.endsWith("/route")){let{dir:e,name:n,ext:i}=o.default.parse(r);r=o.default.posix.join(e,`${n}${t?`-${t}`:""}${i}`,"route")}return r}function m(e,r){let t=e.endsWith("/route"),n=t?e.slice(0,-6):e,o=n.endsWith("/sitemap")?".xml":"";return(r?`${n}/[__metadata_id__]`:`${n}${o}`)+(t?"/route":"")}},2437:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getPathMatch",{enumerable:!0,get:function(){return o}});let n=t(5362);function o(e,r){let t=[],o=(0,n.pathToRegexp)(e,t,{delimiter:"/",sensitive:"boolean"==typeof(null==r?void 0:r.sensitive)&&r.sensitive,strict:null==r?void 0:r.strict}),i=(0,n.regexpToFunction)((null==r?void 0:r.regexModifier)?new RegExp(r.regexModifier(o.source),o.flags):o,t);return(e,n)=>{if("string"!=typeof e)return!1;let o=i(e);if(!o)return!1;if(null==r?void 0:r.removeUnnamedParams)for(let e of t)"number"==typeof e.name&&delete o.params[e.name];return{...n,...o.params}}}},2785:(e,r)=>{"use strict";function t(e){let r={};for(let[t,n]of e.entries()){let e=r[t];void 0===e?r[t]=n:Array.isArray(e)?e.push(n):r[t]=[e,n]}return r}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let r=new URLSearchParams;for(let[t,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)r.append(t,n(e));else r.set(t,n(o));return r}function i(e){for(var r=arguments.length,t=Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];for(let r of t){for(let t of r.keys())e.delete(t);for(let[t,n]of r.entries())e.append(t,n)}return e}Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{assign:function(){return i},searchParamsToUrlQuery:function(){return t},urlQueryToSearchParams:function(){return o}})},2958:(e,r)=>{"use strict";function t(e){return e.replace(/\\/g,"/")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"normalizePathSep",{enumerable:!0,get:function(){return t}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let t=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return t.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3412:(e,r,t)=>{"use strict";t.d(r,{default:()=>rb});var n=t(687),o=t(3210);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function s(...e){return o.useCallback(a(...e),e)}function l(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...n}=e;if(o.isValidElement(t)){var i;let e,s,l=(i=t,(s=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(s=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,r){let t={...r};for(let n in r){let o=e[n],i=r[n];/^on[A-Z]/.test(n)?o&&i?t[n]=(...e)=>{let r=i(...e);return o(...e),r}:o&&(t[n]=o):"style"===n?t[n]={...o,...i}:"className"===n&&(t[n]=[o,i].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==o.Fragment&&(c.ref=r?a(r,l):l),o.cloneElement(t,c)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:i,...a}=e,s=o.Children.toArray(i),l=s.find(d);if(l){let e=l.props.children,i=s.map(r=>r!==l?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...a,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,i):null})}return(0,n.jsx)(r,{...a,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}var c=l("Slot"),u=Symbol("radix.slottable");function d(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}function p(){for(var e,r,t=0,n="",o=arguments.length;t<o;t++)(e=arguments[t])&&(r=function e(r){var t,n,o="";if("string"==typeof r||"number"==typeof r)o+=r;else if("object"==typeof r)if(Array.isArray(r)){var i=r.length;for(t=0;t<i;t++)r[t]&&(n=e(r[t]))&&(o&&(o+=" "),o+=n)}else for(n in r)r[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=r);return n}let f=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,m=e=>{let r=v(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),h(t,r)||b(e)},getConflictingClassGroupIds:(e,r)=>{let o=t[e]||[];return r&&n[e]?[...o,...n[e]]:o}}},h=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],n=r.nextPart.get(t),o=n?h(e.slice(1),n):void 0;if(o)return o;if(0===r.validators.length)return;let i=e.join("-");return r.validators.find(({validator:e})=>e(i))?.classGroupId},g=/^\[(.+)\]$/,b=e=>{if(g.test(e)){let r=g.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},v=e=>{let{theme:r,classGroups:t}=e,n={nextPart:new Map,validators:[]};for(let e in t)x(t[e],n,e,r);return n},x=(e,r,t,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:y(r,e)).classGroupId=t;return}if("function"==typeof e)return w(e)?void x(e(n),r,t,n):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,o])=>{x(o,y(r,e),t,n)})})},y=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},w=e=>e.isThemeGetter,E=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,n=new Map,o=(o,i)=>{t.set(o,i),++r>e&&(r=0,n=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=n.get(e))?(o(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):o(e,r)}}},k=e=>{let{prefix:r,experimentalParseClassName:t}=e,n=e=>{let r,t=[],n=0,o=0,i=0;for(let a=0;a<e.length;a++){let s=e[a];if(0===n&&0===o){if(":"===s){t.push(e.slice(i,a)),i=a+1;continue}if("/"===s){r=a;continue}}"["===s?n++:"]"===s?n--:"("===s?o++:")"===s&&o--}let a=0===t.length?e:e.substring(i),s=R(a);return{modifiers:t,hasImportantModifier:s!==a,baseClassName:s,maybePostfixModifierPosition:r&&r>i?r-i:void 0}};if(r){let e=r+":",t=n;n=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=n;n=r=>t({className:r,parseClassName:e})}return n},R=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,P=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],n=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...n.sort(),e),n=[]):n.push(e)}),t.push(...n.sort()),t}},_=e=>({cache:E(e.cacheSize),parseClassName:k(e),sortModifiers:P(e),...m(e)}),j=/\s+/,S=(e,r)=>{let{parseClassName:t,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:i}=r,a=[],s=e.trim().split(j),l="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:c,modifiers:u,hasImportantModifier:d,baseClassName:p,maybePostfixModifierPosition:f}=t(r);if(c){l=r+(l.length>0?" "+l:l);continue}let m=!!f,h=n(m?p.substring(0,f):p);if(!h){if(!m||!(h=n(p))){l=r+(l.length>0?" "+l:l);continue}m=!1}let g=i(u).join(":"),b=d?g+"!":g,v=b+h;if(a.includes(v))continue;a.push(v);let x=o(h,m);for(let e=0;e<x.length;++e){let r=x[e];a.push(b+r)}l=r+(l.length>0?" "+l:l)}return l};function A(){let e,r,t=0,n="";for(;t<arguments.length;)(e=arguments[t++])&&(r=N(e))&&(n&&(n+=" "),n+=r);return n}let N=e=>{let r;if("string"==typeof e)return e;let t="";for(let n=0;n<e.length;n++)e[n]&&(r=N(e[n]))&&(t&&(t+=" "),t+=r);return t},T=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},O=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,C=/^\((?:(\w[\w-]*):)?(.+)\)$/i,z=/^\d+\/\d+$/,M=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,I=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,D=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,$=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,L=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,U=e=>z.test(e),W=e=>!!e&&!Number.isNaN(Number(e)),q=e=>!!e&&Number.isInteger(Number(e)),F=e=>e.endsWith("%")&&W(e.slice(0,-1)),X=e=>M.test(e),H=()=>!0,G=e=>I.test(e)&&!D.test(e),V=()=>!1,K=e=>$.test(e),Y=e=>L.test(e),B=e=>!Z(e)&&!eo(e),Q=e=>ed(e,eh,V),Z=e=>O.test(e),J=e=>ed(e,eg,G),ee=e=>ed(e,eb,W),er=e=>ed(e,ef,V),et=e=>ed(e,em,Y),en=e=>ed(e,ex,K),eo=e=>C.test(e),ei=e=>ep(e,eg),ea=e=>ep(e,ev),es=e=>ep(e,ef),el=e=>ep(e,eh),ec=e=>ep(e,em),eu=e=>ep(e,ex,!0),ed=(e,r,t)=>{let n=O.exec(e);return!!n&&(n[1]?r(n[1]):t(n[2]))},ep=(e,r,t=!1)=>{let n=C.exec(e);return!!n&&(n[1]?r(n[1]):t)},ef=e=>"position"===e||"percentage"===e,em=e=>"image"===e||"url"===e,eh=e=>"length"===e||"size"===e||"bg-size"===e,eg=e=>"length"===e,eb=e=>"number"===e,ev=e=>"family-name"===e,ex=e=>"shadow"===e;Symbol.toStringTag;let ey=function(e,...r){let t,n,o,i=function(s){return n=(t=_(r.reduce((e,r)=>r(e),e()))).cache.get,o=t.cache.set,i=a,a(s)};function a(e){let r=n(e);if(r)return r;let i=S(e,t);return o(e,i),i}return function(){return i(A.apply(null,arguments))}}(()=>{let e=T("color"),r=T("font"),t=T("text"),n=T("font-weight"),o=T("tracking"),i=T("leading"),a=T("breakpoint"),s=T("container"),l=T("spacing"),c=T("radius"),u=T("shadow"),d=T("inset-shadow"),p=T("text-shadow"),f=T("drop-shadow"),m=T("blur"),h=T("perspective"),g=T("aspect"),b=T("ease"),v=T("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],y=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],w=()=>[...y(),eo,Z],E=()=>["auto","hidden","clip","visible","scroll"],k=()=>["auto","contain","none"],R=()=>[eo,Z,l],P=()=>[U,"full","auto",...R()],_=()=>[q,"none","subgrid",eo,Z],j=()=>["auto",{span:["full",q,eo,Z]},q,eo,Z],S=()=>[q,"auto",eo,Z],A=()=>["auto","min","max","fr",eo,Z],N=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],O=()=>["start","end","center","stretch","center-safe","end-safe"],C=()=>["auto",...R()],z=()=>[U,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...R()],M=()=>[e,eo,Z],I=()=>[...y(),es,er,{position:[eo,Z]}],D=()=>["no-repeat",{repeat:["","x","y","space","round"]}],$=()=>["auto","cover","contain",el,Q,{size:[eo,Z]}],L=()=>[F,ei,J],G=()=>["","none","full",c,eo,Z],V=()=>["",W,ei,J],K=()=>["solid","dashed","dotted","double"],Y=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[W,F,es,er],ep=()=>["","none",m,eo,Z],ef=()=>["none",W,eo,Z],em=()=>["none",W,eo,Z],eh=()=>[W,eo,Z],eg=()=>[U,"full",...R()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[X],breakpoint:[X],color:[H],container:[X],"drop-shadow":[X],ease:["in","out","in-out"],font:[B],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[X],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[X],shadow:[X],spacing:["px",W],text:[X],"text-shadow":[X],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",U,Z,eo,g]}],container:["container"],columns:[{columns:[W,Z,eo,s]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:P()}],"inset-x":[{"inset-x":P()}],"inset-y":[{"inset-y":P()}],start:[{start:P()}],end:[{end:P()}],top:[{top:P()}],right:[{right:P()}],bottom:[{bottom:P()}],left:[{left:P()}],visibility:["visible","invisible","collapse"],z:[{z:[q,"auto",eo,Z]}],basis:[{basis:[U,"full","auto",s,...R()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[W,U,"auto","initial","none",Z]}],grow:[{grow:["",W,eo,Z]}],shrink:[{shrink:["",W,eo,Z]}],order:[{order:[q,"first","last","none",eo,Z]}],"grid-cols":[{"grid-cols":_()}],"col-start-end":[{col:j()}],"col-start":[{"col-start":S()}],"col-end":[{"col-end":S()}],"grid-rows":[{"grid-rows":_()}],"row-start-end":[{row:j()}],"row-start":[{"row-start":S()}],"row-end":[{"row-end":S()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":A()}],"auto-rows":[{"auto-rows":A()}],gap:[{gap:R()}],"gap-x":[{"gap-x":R()}],"gap-y":[{"gap-y":R()}],"justify-content":[{justify:[...N(),"normal"]}],"justify-items":[{"justify-items":[...O(),"normal"]}],"justify-self":[{"justify-self":["auto",...O()]}],"align-content":[{content:["normal",...N()]}],"align-items":[{items:[...O(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...O(),{baseline:["","last"]}]}],"place-content":[{"place-content":N()}],"place-items":[{"place-items":[...O(),"baseline"]}],"place-self":[{"place-self":["auto",...O()]}],p:[{p:R()}],px:[{px:R()}],py:[{py:R()}],ps:[{ps:R()}],pe:[{pe:R()}],pt:[{pt:R()}],pr:[{pr:R()}],pb:[{pb:R()}],pl:[{pl:R()}],m:[{m:C()}],mx:[{mx:C()}],my:[{my:C()}],ms:[{ms:C()}],me:[{me:C()}],mt:[{mt:C()}],mr:[{mr:C()}],mb:[{mb:C()}],ml:[{ml:C()}],"space-x":[{"space-x":R()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":R()}],"space-y-reverse":["space-y-reverse"],size:[{size:z()}],w:[{w:[s,"screen",...z()]}],"min-w":[{"min-w":[s,"screen","none",...z()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[a]},...z()]}],h:[{h:["screen","lh",...z()]}],"min-h":[{"min-h":["screen","lh","none",...z()]}],"max-h":[{"max-h":["screen","lh",...z()]}],"font-size":[{text:["base",t,ei,J]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,eo,ee]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",F,Z]}],"font-family":[{font:[ea,Z,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,eo,Z]}],"line-clamp":[{"line-clamp":[W,"none",eo,ee]}],leading:[{leading:[i,...R()]}],"list-image":[{"list-image":["none",eo,Z]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",eo,Z]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:M()}],"text-color":[{text:M()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...K(),"wavy"]}],"text-decoration-thickness":[{decoration:[W,"from-font","auto",eo,J]}],"text-decoration-color":[{decoration:M()}],"underline-offset":[{"underline-offset":[W,"auto",eo,Z]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:R()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",eo,Z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",eo,Z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:I()}],"bg-repeat":[{bg:D()}],"bg-size":[{bg:$()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},q,eo,Z],radial:["",eo,Z],conic:[q,eo,Z]},ec,et]}],"bg-color":[{bg:M()}],"gradient-from-pos":[{from:L()}],"gradient-via-pos":[{via:L()}],"gradient-to-pos":[{to:L()}],"gradient-from":[{from:M()}],"gradient-via":[{via:M()}],"gradient-to":[{to:M()}],rounded:[{rounded:G()}],"rounded-s":[{"rounded-s":G()}],"rounded-e":[{"rounded-e":G()}],"rounded-t":[{"rounded-t":G()}],"rounded-r":[{"rounded-r":G()}],"rounded-b":[{"rounded-b":G()}],"rounded-l":[{"rounded-l":G()}],"rounded-ss":[{"rounded-ss":G()}],"rounded-se":[{"rounded-se":G()}],"rounded-ee":[{"rounded-ee":G()}],"rounded-es":[{"rounded-es":G()}],"rounded-tl":[{"rounded-tl":G()}],"rounded-tr":[{"rounded-tr":G()}],"rounded-br":[{"rounded-br":G()}],"rounded-bl":[{"rounded-bl":G()}],"border-w":[{border:V()}],"border-w-x":[{"border-x":V()}],"border-w-y":[{"border-y":V()}],"border-w-s":[{"border-s":V()}],"border-w-e":[{"border-e":V()}],"border-w-t":[{"border-t":V()}],"border-w-r":[{"border-r":V()}],"border-w-b":[{"border-b":V()}],"border-w-l":[{"border-l":V()}],"divide-x":[{"divide-x":V()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":V()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...K(),"hidden","none"]}],"divide-style":[{divide:[...K(),"hidden","none"]}],"border-color":[{border:M()}],"border-color-x":[{"border-x":M()}],"border-color-y":[{"border-y":M()}],"border-color-s":[{"border-s":M()}],"border-color-e":[{"border-e":M()}],"border-color-t":[{"border-t":M()}],"border-color-r":[{"border-r":M()}],"border-color-b":[{"border-b":M()}],"border-color-l":[{"border-l":M()}],"divide-color":[{divide:M()}],"outline-style":[{outline:[...K(),"none","hidden"]}],"outline-offset":[{"outline-offset":[W,eo,Z]}],"outline-w":[{outline:["",W,ei,J]}],"outline-color":[{outline:M()}],shadow:[{shadow:["","none",u,eu,en]}],"shadow-color":[{shadow:M()}],"inset-shadow":[{"inset-shadow":["none",d,eu,en]}],"inset-shadow-color":[{"inset-shadow":M()}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:M()}],"ring-offset-w":[{"ring-offset":[W,J]}],"ring-offset-color":[{"ring-offset":M()}],"inset-ring-w":[{"inset-ring":V()}],"inset-ring-color":[{"inset-ring":M()}],"text-shadow":[{"text-shadow":["none",p,eu,en]}],"text-shadow-color":[{"text-shadow":M()}],opacity:[{opacity:[W,eo,Z]}],"mix-blend":[{"mix-blend":[...Y(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":Y()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[W]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":M()}],"mask-image-linear-to-color":[{"mask-linear-to":M()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":M()}],"mask-image-t-to-color":[{"mask-t-to":M()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":M()}],"mask-image-r-to-color":[{"mask-r-to":M()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":M()}],"mask-image-b-to-color":[{"mask-b-to":M()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":M()}],"mask-image-l-to-color":[{"mask-l-to":M()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":M()}],"mask-image-x-to-color":[{"mask-x-to":M()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":M()}],"mask-image-y-to-color":[{"mask-y-to":M()}],"mask-image-radial":[{"mask-radial":[eo,Z]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":M()}],"mask-image-radial-to-color":[{"mask-radial-to":M()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":y()}],"mask-image-conic-pos":[{"mask-conic":[W]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":M()}],"mask-image-conic-to-color":[{"mask-conic-to":M()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:I()}],"mask-repeat":[{mask:D()}],"mask-size":[{mask:$()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",eo,Z]}],filter:[{filter:["","none",eo,Z]}],blur:[{blur:ep()}],brightness:[{brightness:[W,eo,Z]}],contrast:[{contrast:[W,eo,Z]}],"drop-shadow":[{"drop-shadow":["","none",f,eu,en]}],"drop-shadow-color":[{"drop-shadow":M()}],grayscale:[{grayscale:["",W,eo,Z]}],"hue-rotate":[{"hue-rotate":[W,eo,Z]}],invert:[{invert:["",W,eo,Z]}],saturate:[{saturate:[W,eo,Z]}],sepia:[{sepia:["",W,eo,Z]}],"backdrop-filter":[{"backdrop-filter":["","none",eo,Z]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[W,eo,Z]}],"backdrop-contrast":[{"backdrop-contrast":[W,eo,Z]}],"backdrop-grayscale":[{"backdrop-grayscale":["",W,eo,Z]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[W,eo,Z]}],"backdrop-invert":[{"backdrop-invert":["",W,eo,Z]}],"backdrop-opacity":[{"backdrop-opacity":[W,eo,Z]}],"backdrop-saturate":[{"backdrop-saturate":[W,eo,Z]}],"backdrop-sepia":[{"backdrop-sepia":["",W,eo,Z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":R()}],"border-spacing-x":[{"border-spacing-x":R()}],"border-spacing-y":[{"border-spacing-y":R()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",eo,Z]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[W,"initial",eo,Z]}],ease:[{ease:["linear","initial",b,eo,Z]}],delay:[{delay:[W,eo,Z]}],animate:[{animate:["none",v,eo,Z]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,eo,Z]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:ef()}],"rotate-x":[{"rotate-x":ef()}],"rotate-y":[{"rotate-y":ef()}],"rotate-z":[{"rotate-z":ef()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eh()}],"skew-x":[{"skew-x":eh()}],"skew-y":[{"skew-y":eh()}],transform:[{transform:[eo,Z,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:M()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:M()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",eo,Z]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":R()}],"scroll-mx":[{"scroll-mx":R()}],"scroll-my":[{"scroll-my":R()}],"scroll-ms":[{"scroll-ms":R()}],"scroll-me":[{"scroll-me":R()}],"scroll-mt":[{"scroll-mt":R()}],"scroll-mr":[{"scroll-mr":R()}],"scroll-mb":[{"scroll-mb":R()}],"scroll-ml":[{"scroll-ml":R()}],"scroll-p":[{"scroll-p":R()}],"scroll-px":[{"scroll-px":R()}],"scroll-py":[{"scroll-py":R()}],"scroll-ps":[{"scroll-ps":R()}],"scroll-pe":[{"scroll-pe":R()}],"scroll-pt":[{"scroll-pt":R()}],"scroll-pr":[{"scroll-pr":R()}],"scroll-pb":[{"scroll-pb":R()}],"scroll-pl":[{"scroll-pl":R()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",eo,Z]}],fill:[{fill:["none",...M()]}],"stroke-w":[{stroke:[W,ei,J,ee]}],stroke:[{stroke:["none",...M()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ew(...e){return ey(p(e))}let eE=((e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return p(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:i}=r,a=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let a=f(r)||f(n);return o[e][a]}),s=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return p(e,a,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...o}=r;return Object.entries(o).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...s}[r]):({...i,...s})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)})("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function ek({className:e,variant:r,size:t,asChild:o=!1,...i}){return(0,n.jsx)(o?c:"button",{"data-slot":"button",className:ew(eE({variant:r,size:t,className:e})),...i})}function eR({className:e,...r}){return(0,n.jsx)("textarea",{"data-slot":"textarea",className:ew("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...r})}t(1215);var eP=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=l(`Primitive.${r}`),i=o.forwardRef((e,o)=>{let{asChild:i,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(i?t:r,{...a,ref:o})});return i.displayName=`Primitive.${r}`,{...e,[r]:i}},{}),e_=globalThis?.document?o.useLayoutEffect:()=>{},ej=e=>{let{present:r,children:t}=e,n=function(e){var r,t;let[n,i]=o.useState(),a=o.useRef(null),s=o.useRef(e),l=o.useRef("none"),[c,u]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,r)=>t[e][r]??e,r));return o.useEffect(()=>{let e=eS(a.current);l.current="mounted"===c?e:"none"},[c]),e_(()=>{let r=a.current,t=s.current;if(t!==e){let n=l.current,o=eS(r);e?u("MOUNT"):"none"===o||r?.display==="none"?u("UNMOUNT"):t&&n!==o?u("ANIMATION_OUT"):u("UNMOUNT"),s.current=e}},[e,u]),e_(()=>{if(n){let e,r=n.ownerDocument.defaultView??window,t=t=>{let o=eS(a.current).includes(t.animationName);if(t.target===n&&o&&(u("ANIMATION_END"),!s.current)){let t=n.style.animationFillMode;n.style.animationFillMode="forwards",e=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=t)})}},o=e=>{e.target===n&&(l.current=eS(a.current))};return n.addEventListener("animationstart",o),n.addEventListener("animationcancel",t),n.addEventListener("animationend",t),()=>{r.clearTimeout(e),n.removeEventListener("animationstart",o),n.removeEventListener("animationcancel",t),n.removeEventListener("animationend",t)}}u("ANIMATION_END")},[n,u]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(r),i="function"==typeof t?t({present:n.isPresent}):o.Children.only(t),a=s(n.ref,function(e){let r=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,t=r&&"isReactWarning"in r&&r.isReactWarning;return t?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof t||n.isPresent?o.cloneElement(i,{ref:a}):null};function eS(e){return e?.animationName||"none"}function eA(e){let r=o.useRef(e);return o.useEffect(()=>{r.current=e}),o.useMemo(()=>(...e)=>r.current?.(...e),[])}ej.displayName="Presence";var eN=o.createContext(void 0);function eT(e,r,{checkForDefaultPrevented:t=!0}={}){return function(n){if(e?.(n),!1===t||!n.defaultPrevented)return r?.(n)}}var eO="ScrollArea",[eC,ez]=function(e,r=[]){let t=[],i=()=>{let r=t.map(e=>o.createContext(e));return function(t){let n=t?.[e]||r;return o.useMemo(()=>({[`__scope${e}`]:{...t,[e]:n}}),[t,n])}};return i.scopeName=e,[function(r,i){let a=o.createContext(i),s=t.length;t=[...t,i];let l=r=>{let{scope:t,children:i,...l}=r,c=t?.[e]?.[s]||a,u=o.useMemo(()=>l,Object.values(l));return(0,n.jsx)(c.Provider,{value:u,children:i})};return l.displayName=r+"Provider",[l,function(t,n){let l=n?.[e]?.[s]||a,c=o.useContext(l);if(c)return c;if(void 0!==i)return i;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=t.reduce((r,{useScope:t,scopeName:n})=>{let o=t(e)[`__scope${n}`];return{...r,...o}},{});return o.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return t.scopeName=r.scopeName,t}(i,...r)]}(eO),[eM,eI]=eC(eO),eD=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:i="hover",dir:a,scrollHideDelay:l=600,...c}=e,[u,d]=o.useState(null),[p,f]=o.useState(null),[m,h]=o.useState(null),[g,b]=o.useState(null),[v,x]=o.useState(null),[y,w]=o.useState(0),[E,k]=o.useState(0),[R,P]=o.useState(!1),[_,j]=o.useState(!1),S=s(r,e=>d(e)),A=function(e){let r=o.useContext(eN);return e||r||"ltr"}(a);return(0,n.jsx)(eM,{scope:t,type:i,dir:A,scrollHideDelay:l,scrollArea:u,viewport:p,onViewportChange:f,content:m,onContentChange:h,scrollbarX:g,onScrollbarXChange:b,scrollbarXEnabled:R,onScrollbarXEnabledChange:P,scrollbarY:v,onScrollbarYChange:x,scrollbarYEnabled:_,onScrollbarYEnabledChange:j,onCornerWidthChange:w,onCornerHeightChange:k,children:(0,n.jsx)(eP.div,{dir:A,...c,ref:S,style:{position:"relative","--radix-scroll-area-corner-width":y+"px","--radix-scroll-area-corner-height":E+"px",...e.style}})})});eD.displayName=eO;var e$="ScrollAreaViewport",eL=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:i,nonce:a,...l}=e,c=eI(e$,t),u=s(r,o.useRef(null),c.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,n.jsx)(eP.div,{"data-radix-scroll-area-viewport":"",...l,ref:u,style:{overflowX:c.scrollbarXEnabled?"scroll":"hidden",overflowY:c.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:c.onContentChange,style:{minWidth:"100%",display:"table"},children:i})})]})});eL.displayName=e$;var eU="ScrollAreaScrollbar",eW=o.forwardRef((e,r)=>{let{forceMount:t,...i}=e,a=eI(eU,e.__scopeScrollArea),{onScrollbarXEnabledChange:s,onScrollbarYEnabledChange:l}=a,c="horizontal"===e.orientation;return o.useEffect(()=>(c?s(!0):l(!0),()=>{c?s(!1):l(!1)}),[c,s,l]),"hover"===a.type?(0,n.jsx)(eq,{...i,ref:r,forceMount:t}):"scroll"===a.type?(0,n.jsx)(eF,{...i,ref:r,forceMount:t}):"auto"===a.type?(0,n.jsx)(eX,{...i,ref:r,forceMount:t}):"always"===a.type?(0,n.jsx)(eH,{...i,ref:r}):null});eW.displayName=eU;var eq=o.forwardRef((e,r)=>{let{forceMount:t,...i}=e,a=eI(eU,e.__scopeScrollArea),[s,l]=o.useState(!1);return o.useEffect(()=>{let e=a.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),l(!0)},n=()=>{r=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",n)}}},[a.scrollArea,a.scrollHideDelay]),(0,n.jsx)(ej,{present:t||s,children:(0,n.jsx)(eX,{"data-state":s?"visible":"hidden",...i,ref:r})})}),eF=o.forwardRef((e,r)=>{var t,i;let{forceMount:a,...s}=e,l=eI(eU,e.__scopeScrollArea),c="horizontal"===e.orientation,u=e7(()=>p("SCROLL_END"),100),[d,p]=(t="hidden",i={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>i[e][r]??e,t));return o.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>p("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,l.scrollHideDelay,p]),o.useEffect(()=>{let e=l.viewport,r=c?"scrollLeft":"scrollTop";if(e){let t=e[r],n=()=>{let n=e[r];t!==n&&(p("SCROLL"),u()),t=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[l.viewport,c,p,u]),(0,n.jsx)(ej,{present:a||"hidden"!==d,children:(0,n.jsx)(eH,{"data-state":"hidden"===d?"hidden":"visible",...s,ref:r,onPointerEnter:eT(e.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:eT(e.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),eX=o.forwardRef((e,r)=>{let t=eI(eU,e.__scopeScrollArea),{forceMount:i,...a}=e,[s,l]=o.useState(!1),c="horizontal"===e.orientation,u=e7(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;l(c?e:r)}},10);return re(t.viewport,u),re(t.content,u),(0,n.jsx)(ej,{present:i||s,children:(0,n.jsx)(eH,{"data-state":s?"visible":"hidden",...a,ref:r})})}),eH=o.forwardRef((e,r)=>{let{orientation:t="vertical",...i}=e,a=eI(eU,e.__scopeScrollArea),s=o.useRef(null),l=o.useRef(0),[c,u]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=e3(c.viewport,c.content),p={...i,sizes:c,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>s.current=e,onThumbPointerUp:()=>l.current=0,onThumbPointerDown:e=>l.current=e};function f(e,r){return function(e,r,t,n="ltr"){let o=e5(t),i=r||o/2,a=t.scrollbar.paddingStart+i,s=t.scrollbar.size-t.scrollbar.paddingEnd-(o-i),l=t.content-t.viewport;return e6([a,s],"ltr"===n?[0,l]:[-1*l,0])(e)}(e,l.current,c,r)}return"horizontal"===t?(0,n.jsx)(eG,{...p,ref:r,onThumbPositionChange:()=>{if(a.viewport&&s.current){let e=e9(a.viewport.scrollLeft,c,a.dir);s.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=f(e,a.dir))}}):"vertical"===t?(0,n.jsx)(eV,{...p,ref:r,onThumbPositionChange:()=>{if(a.viewport&&s.current){let e=e9(a.viewport.scrollTop,c);s.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=f(e))}}):null}),eG=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:i,...a}=e,l=eI(eU,e.__scopeScrollArea),[c,u]=o.useState(),d=o.useRef(null),p=s(r,d,l.onScrollbarXChange);return o.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,n.jsx)(eB,{"data-orientation":"horizontal",...a,ref:p,sizes:t,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":e5(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(l.viewport){let n=l.viewport.scrollLeft+r.deltaX;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&l.viewport&&c&&i({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:e4(c.paddingLeft),paddingEnd:e4(c.paddingRight)}})}})}),eV=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:i,...a}=e,l=eI(eU,e.__scopeScrollArea),[c,u]=o.useState(),d=o.useRef(null),p=s(r,d,l.onScrollbarYChange);return o.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,n.jsx)(eB,{"data-orientation":"vertical",...a,ref:p,sizes:t,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":e5(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(l.viewport){let n=l.viewport.scrollTop+r.deltaY;e.onWheelScroll(n),function(e,r){return e>0&&e<r}(n,t)&&r.preventDefault()}},onResize:()=>{d.current&&l.viewport&&c&&i({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:e4(c.paddingTop),paddingEnd:e4(c.paddingBottom)}})}})}),[eK,eY]=eC(eU),eB=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:i,hasThumb:a,onThumbChange:l,onThumbPointerUp:c,onThumbPointerDown:u,onThumbPositionChange:d,onDragScroll:p,onWheelScroll:f,onResize:m,...h}=e,g=eI(eU,t),[b,v]=o.useState(null),x=s(r,e=>v(e)),y=o.useRef(null),w=o.useRef(""),E=g.viewport,k=i.content-i.viewport,R=eA(f),P=eA(d),_=e7(m,10);function j(e){y.current&&p({x:e.clientX-y.current.left,y:e.clientY-y.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;b?.contains(r)&&R(e,k)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[E,b,k,R]),o.useEffect(P,[i,P]),re(b,_),re(g.content,_),(0,n.jsx)(eK,{scope:t,scrollbar:b,hasThumb:a,onThumbChange:eA(l),onThumbPointerUp:eA(c),onThumbPositionChange:P,onThumbPointerDown:eA(u),children:(0,n.jsx)(eP.div,{...h,ref:x,style:{position:"absolute",...h.style},onPointerDown:eT(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),y.current=b.getBoundingClientRect(),w.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",g.viewport&&(g.viewport.style.scrollBehavior="auto"),j(e))}),onPointerMove:eT(e.onPointerMove,j),onPointerUp:eT(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=w.current,g.viewport&&(g.viewport.style.scrollBehavior=""),y.current=null})})})}),eQ="ScrollAreaThumb",eZ=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,i=eY(eQ,e.__scopeScrollArea);return(0,n.jsx)(ej,{present:t||i.hasThumb,children:(0,n.jsx)(eJ,{ref:r,...o})})}),eJ=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:i,...a}=e,l=eI(eQ,t),c=eY(eQ,t),{onThumbPositionChange:u}=c,d=s(r,e=>c.onThumbChange(e)),p=o.useRef(void 0),f=e7(()=>{p.current&&(p.current(),p.current=void 0)},100);return o.useEffect(()=>{let e=l.viewport;if(e){let r=()=>{f(),p.current||(p.current=e8(e,u),u())};return u(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[l.viewport,f,u]),(0,n.jsx)(eP.div,{"data-state":c.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:eT(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,n=e.clientY-r.top;c.onThumbPointerDown({x:t,y:n})}),onPointerUp:eT(e.onPointerUp,c.onThumbPointerUp)})});eZ.displayName=eQ;var e0="ScrollAreaCorner",e1=o.forwardRef((e,r)=>{let t=eI(e0,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,n.jsx)(e2,{...e,ref:r}):null});e1.displayName=e0;var e2=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...i}=e,a=eI(e0,t),[s,l]=o.useState(0),[c,u]=o.useState(0),d=!!(s&&c);return re(a.scrollbarX,()=>{let e=a.scrollbarX?.offsetHeight||0;a.onCornerHeightChange(e),u(e)}),re(a.scrollbarY,()=>{let e=a.scrollbarY?.offsetWidth||0;a.onCornerWidthChange(e),l(e)}),d?(0,n.jsx)(eP.div,{...i,ref:r,style:{width:s,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function e4(e){return e?parseInt(e,10):0}function e3(e,r){let t=e/r;return isNaN(t)?0:t}function e5(e){let r=e3(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function e9(e,r,t="ltr"){let n=e5(r),o=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,i=r.scrollbar.size-o,a=r.content-r.viewport,s=function(e,[r,t]){return Math.min(t,Math.max(r,e))}(e,"ltr"===t?[0,a]:[-1*a,0]);return e6([0,a],[0,i-n])(s)}function e6(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let n=(r[1]-r[0])/(e[1]-e[0]);return r[0]+n*(t-e[0])}}var e8=(e,r=()=>{})=>{let t={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},a=t.left!==i.left,s=t.top!==i.top;(a||s)&&r(),t=i,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function e7(e,r){let t=eA(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function re(e,r){let t=eA(r);e_(()=>{let r=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return n.observe(e),()=>{window.cancelAnimationFrame(r),n.unobserve(e)}}},[e,t])}function rr({className:e,children:r,...t}){return(0,n.jsxs)(eD,{"data-slot":"scroll-area",className:ew("relative",e),...t,children:[(0,n.jsx)(eL,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:r}),(0,n.jsx)(rt,{}),(0,n.jsx)(e1,{})]})}function rt({className:e,orientation:r="vertical",...t}){return(0,n.jsx)(eW,{"data-slot":"scroll-area-scrollbar",orientation:r,className:ew("flex touch-none p-px transition-colors select-none","vertical"===r&&"h-full w-2.5 border-l border-l-transparent","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent",e),...t,children:(0,n.jsx)(eZ,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}let rn=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ro=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),ri=e=>{let r=ro(e);return r.charAt(0).toUpperCase()+r.slice(1)},ra=(...e)=>e.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim(),rs=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var rl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let rc=(0,o.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:n,className:i="",children:a,iconNode:s,...l},c)=>(0,o.createElement)("svg",{ref:c,...rl,width:r,height:r,stroke:e,strokeWidth:n?24*Number(t)/Number(r):t,className:ra("lucide",i),...!a&&!rs(l)&&{"aria-hidden":"true"},...l},[...s.map(([e,r])=>(0,o.createElement)(e,r)),...Array.isArray(a)?a:[a]])),ru=(e,r)=>{let t=(0,o.forwardRef)(({className:t,...n},i)=>(0,o.createElement)(rc,{ref:i,iconNode:r,className:ra(`lucide-${rn(ri(e))}`,`lucide-${e}`,t),...n}));return t.displayName=ri(e),t},rd=ru("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),rp=ru("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]),rf=ru("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),rm=ru("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),rh=ru("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),rg={async sendMessage({question:e,history:r}){let t=process.env.NEXT_PUBLIC_API_URL||"http://localhost:5005";try{let n=await fetch(`${t}/api/rag`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({question:e,history:r})});if(!n.ok)throw Error(`HTTP error! status: ${n.status}`);return await n.json()}catch(e){throw console.error("Error sending message:",e),Error("Failed to send message. Please try again.")}}};function rb(){let[e,r]=(0,o.useState)([]),[t,i]=(0,o.useState)(!1),[a,s]=(0,o.useState)(""),l=(0,o.useRef)(null),c=async t=>{let n={id:Date.now().toString(),role:"user",content:t,timestamp:new Date};r(e=>[...e,n]),i(!0);try{let n=e.map(e=>({role:e.role,content:e.content})),o=await rg.sendMessage({question:t,history:n}),i={id:(Date.now()+1).toString(),role:"assistant",content:o.answer,timestamp:new Date};r(e=>[...e,i])}catch(t){let e={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error while processing your request. Please try again.",timestamp:new Date};r(r=>[...r,e]),console.error("Chat error:",t)}finally{i(!1)}},u=()=>{a.trim()&&!t&&(c(a.trim()),s(""))};return(0,n.jsxs)("div",{className:"h-screen flex flex-col bg-gray-50",children:[(0,n.jsx)("div",{className:"bg-white border-b p-4",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto flex items-center gap-2",children:[(0,n.jsx)(rd,{size:24,className:"text-blue-600"}),(0,n.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:"RAG Chat Assistant"})]})}),(0,n.jsxs)("div",{className:"flex-1 flex flex-col min-h-0",children:[(0,n.jsx)(rr,{className:"flex-1 p-4",ref:l,children:(0,n.jsx)("div",{className:"max-w-4xl mx-auto",children:0===e.length?(0,n.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)(rd,{size:48,className:"mx-auto mb-4 opacity-50"}),(0,n.jsx)("p",{children:"Start a conversation by asking a question!"})]})}):(0,n.jsxs)("div",{className:"space-y-4",children:[e.map(e=>(0,n.jsxs)("div",{className:`flex gap-3 ${"user"===e.role?"justify-end":"justify-start"}`,children:["assistant"===e.role&&(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0",children:(0,n.jsx)(rp,{size:16,className:"text-blue-600"})}),(0,n.jsxs)("div",{className:`max-w-[70%] ${"user"===e.role?"order-first":""}`,children:[(0,n.jsx)("div",{className:`rounded-lg px-4 py-2 ${"user"===e.role?"bg-blue-600 text-white ml-auto":"bg-white border text-gray-900"}`,children:(0,n.jsx)("p",{className:"text-sm whitespace-pre-wrap",children:e.content})}),(0,n.jsx)("div",{className:`text-xs text-gray-500 mt-1 ${"user"===e.role?"text-right":"text-left"}`,children:e.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]}),"user"===e.role&&(0,n.jsx)("div",{className:"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0",children:(0,n.jsx)(rf,{size:16,className:"text-white"})})]},e.id)),t&&(0,n.jsx)("div",{className:"flex justify-start mb-4",children:(0,n.jsxs)("div",{className:"flex items-center gap-2 bg-gray-100 rounded-lg p-3",children:[(0,n.jsx)(rm,{size:16,className:"animate-spin"}),(0,n.jsx)("span",{className:"text-sm text-gray-600",children:"Thinking..."})]})})]})})}),(0,n.jsx)("div",{className:"border-t bg-white p-4",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto flex gap-2",children:[(0,n.jsx)(eR,{value:a,onChange:e=>s(e.target.value),onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),u())},placeholder:"Type your message...",disabled:t,className:"flex-1 min-h-[44px] max-h-32 resize-none",rows:1}),(0,n.jsx)(ek,{onClick:u,disabled:!a.trim()||t,className:"bg-blue-600 hover:bg-blue-700",children:(0,n.jsx)(rh,{size:16})})]})})]})]})}},3736:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"parseRelativeUrl",{enumerable:!0,get:function(){return o}}),t(4827);let n=t(2785);function o(e,r,t){void 0===t&&(t=!0);let o=new URL("http://n"),i=r?new URL(r,o):e.startsWith(".")?new URL("http://n"):o,{pathname:a,searchParams:s,search:l,hash:c,href:u,origin:d}=new URL(e,i);if(d!==o.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:a,query:t?(0,n.searchParamsToUrlQuery)(s):void 0,search:l,hash:c,href:u.slice(d.length)}}},3873:e=>{"use strict";e.exports=require("path")},4054:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});let n=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/i2i/rag-project/frontend/src/components/chat/ChatInterface.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/i2i/rag-project/frontend/src/components/chat/ChatInterface.tsx","default")},4396:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return m},getRouteRegex:function(){return d},parseParameter:function(){return l}});let n=t(6143),o=t(1437),i=t(3293),a=t(2887),s=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let r=e.match(s);return r?c(r[2]):c(e)}function c(e){let r=e.startsWith("[")&&e.endsWith("]");r&&(e=e.slice(1,-1));let t=e.startsWith("...");return t&&(e=e.slice(3)),{key:e,repeat:t,optional:r}}function u(e,r,t){let n={},l=1,u=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(s);if(e&&a&&a[2]){let{key:r,optional:t,repeat:o}=c(a[2]);n[r]={pos:l++,repeat:o,optional:t},u.push("/"+(0,i.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:r,optional:o}=c(a[2]);n[e]={pos:l++,repeat:r,optional:o},t&&a[1]&&u.push("/"+(0,i.escapeStringRegexp)(a[1]));let s=r?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";t&&a[1]&&(s=s.substring(1)),u.push(s)}else u.push("/"+(0,i.escapeStringRegexp)(d));r&&a&&a[3]&&u.push((0,i.escapeStringRegexp)(a[3]))}return{parameterizedRoute:u.join(""),groups:n}}function d(e,r){let{includeSuffix:t=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:o=!1}=void 0===r?{}:r,{parameterizedRoute:i,groups:a}=u(e,t,n),s=i;return o||(s+="(?:/)?"),{re:RegExp("^"+s+"$"),groups:a}}function p(e){let r,{interceptionMarker:t,getSafeRouteKey:n,segment:o,routeKeys:a,keyPrefix:s,backreferenceDuplicateKeys:l}=e,{key:u,optional:d,repeat:p}=c(o),f=u.replace(/\W/g,"");s&&(f=""+s+f);let m=!1;(0===f.length||f.length>30)&&(m=!0),isNaN(parseInt(f.slice(0,1)))||(m=!0),m&&(f=n());let h=f in a;s?a[f]=""+s+u:a[f]=u;let g=t?(0,i.escapeStringRegexp)(t):"";return r=h&&l?"\\k<"+f+">":p?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+g+r+")?":"/"+g+r}function f(e,r,t,l,c){let u,d=(u=0,()=>{let e="",r=++u;for(;r>0;)e+=String.fromCharCode(97+(r-1)%26),r=Math.floor((r-1)/26);return e}),f={},m=[];for(let u of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=o.INTERCEPTION_ROUTE_MARKERS.some(e=>u.startsWith(e)),a=u.match(s);if(e&&a&&a[2])m.push(p({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:f,keyPrefix:r?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(a&&a[2]){l&&a[1]&&m.push("/"+(0,i.escapeStringRegexp)(a[1]));let e=p({getSafeRouteKey:d,segment:a[2],routeKeys:f,keyPrefix:r?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&a[1]&&(e=e.substring(1)),m.push(e)}else m.push("/"+(0,i.escapeStringRegexp)(u));t&&a&&a[3]&&m.push((0,i.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:m.join(""),routeKeys:f}}function m(e,r){var t,n,o;let i=f(e,r.prefixRouteKeys,null!=(t=r.includeSuffix)&&t,null!=(n=r.includePrefix)&&n,null!=(o=r.backreferenceDuplicateKeys)&&o),a=i.namedParameterizedRoute;return r.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,r),namedRegex:"^"+a+"$",routeKeys:i.routeKeys}}function h(e,r){let{parameterizedRoute:t}=u(e,!1,!1),{catchAll:n=!0}=r;if("/"===t)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>l});var n=t(7413),o=t(2202),i=t.n(o),a=t(4988),s=t.n(a);t(1135);let l={title:"RAG Chat Assistant",description:"Intelligent document-based chat assistant using RAG technology"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${i().variable} ${s().variable} antialiased`,children:e})})}},4520:(e,r,t)=>{Promise.resolve().then(t.bind(t,3412))},4722:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{normalizeAppPath:function(){return i},normalizeRscURL:function(){return a}});let n=t(5531),o=t(5499);function i(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,r,t,n)=>!r||(0,o.isGroupSegment)(r)||"@"===r[0]||("page"===r||"route"===r)&&t===n.length-1?e:e+"/"+r,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return b},NormalizeError:function(){return h},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return f},WEB_VITALS:function(){return t},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return i},isResSent:function(){return c},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return u},stringifyError:function(){return x}});let t=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let r,t=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return t||(t=!0,r=e(...o)),r}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:r,port:t}=window.location;return e+"//"+r+(t?":"+t:"")}function s(){let{href:e}=window.location,r=a();return e.substring(r.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function u(e){let r=e.split("?");return r[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(r[1]?"?"+r.slice(1).join("?"):"")}async function d(e,r){let t=r.res||r.ctx&&r.ctx.res;if(!e.getInitialProps)return r.ctx&&r.Component?{pageProps:await d(r.Component,r.ctx)}:{};let n=await e.getInitialProps(r);if(t&&c(t))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let p="undefined"!=typeof performance,f=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class b extends Error{constructor(e,r){super(),this.message="Failed to load static file for page: "+e+" "+r}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var r={};(()=>{function e(e,r){void 0===r&&(r={});for(var t=function(e){for(var r=[],t=0;t<e.length;){var n=e[t];if("*"===n||"+"===n||"?"===n){r.push({type:"MODIFIER",index:t,value:e[t++]});continue}if("\\"===n){r.push({type:"ESCAPED_CHAR",index:t++,value:e[t++]});continue}if("{"===n){r.push({type:"OPEN",index:t,value:e[t++]});continue}if("}"===n){r.push({type:"CLOSE",index:t,value:e[t++]});continue}if(":"===n){for(var o="",i=t+1;i<e.length;){var a=e.charCodeAt(i);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){o+=e[i++];continue}break}if(!o)throw TypeError("Missing parameter name at "+t);r.push({type:"NAME",index:t,value:o}),t=i;continue}if("("===n){var s=1,l="",i=t+1;if("?"===e[i])throw TypeError('Pattern cannot start with "?" at '+i);for(;i<e.length;){if("\\"===e[i]){l+=e[i++]+e[i++];continue}if(")"===e[i]){if(0==--s){i++;break}}else if("("===e[i]&&(s++,"?"!==e[i+1]))throw TypeError("Capturing groups are not allowed at "+i);l+=e[i++]}if(s)throw TypeError("Unbalanced pattern at "+t);if(!l)throw TypeError("Missing pattern at "+t);r.push({type:"PATTERN",index:t,value:l}),t=i;continue}r.push({type:"CHAR",index:t,value:e[t++]})}return r.push({type:"END",index:t,value:""}),r}(e),n=r.prefixes,i=void 0===n?"./":n,a="[^"+o(r.delimiter||"/#?")+"]+?",s=[],l=0,c=0,u="",d=function(e){if(c<t.length&&t[c].type===e)return t[c++].value},p=function(e){var r=d(e);if(void 0!==r)return r;var n=t[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},f=function(){for(var e,r="";e=d("CHAR")||d("ESCAPED_CHAR");)r+=e;return r};c<t.length;){var m=d("CHAR"),h=d("NAME"),g=d("PATTERN");if(h||g){var b=m||"";-1===i.indexOf(b)&&(u+=b,b=""),u&&(s.push(u),u=""),s.push({name:h||l++,prefix:b,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var v=m||d("ESCAPED_CHAR");if(v){u+=v;continue}if(u&&(s.push(u),u=""),d("OPEN")){var b=f(),x=d("NAME")||"",y=d("PATTERN")||"",w=f();p("CLOSE"),s.push({name:x||(y?l++:""),pattern:x&&!y?a:y,prefix:b,suffix:w,modifier:d("MODIFIER")||""});continue}p("END")}return s}function t(e,r){void 0===r&&(r={});var t=i(r),n=r.encode,o=void 0===n?function(e){return e}:n,a=r.validate,s=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",t)});return function(r){for(var t="",n=0;n<e.length;n++){var i=e[n];if("string"==typeof i){t+=i;continue}var a=r?r[i.name]:void 0,c="?"===i.modifier||"*"===i.modifier,u="*"===i.modifier||"+"===i.modifier;if(Array.isArray(a)){if(!u)throw TypeError('Expected "'+i.name+'" to not repeat, but got an array');if(0===a.length){if(c)continue;throw TypeError('Expected "'+i.name+'" to not be empty')}for(var d=0;d<a.length;d++){var p=o(a[d],i);if(s&&!l[n].test(p))throw TypeError('Expected all "'+i.name+'" to match "'+i.pattern+'", but got "'+p+'"');t+=i.prefix+p+i.suffix}continue}if("string"==typeof a||"number"==typeof a){var p=o(String(a),i);if(s&&!l[n].test(p))throw TypeError('Expected "'+i.name+'" to match "'+i.pattern+'", but got "'+p+'"');t+=i.prefix+p+i.suffix;continue}if(!c){var f=u?"an array":"a string";throw TypeError('Expected "'+i.name+'" to be '+f)}}return t}}function n(e,r,t){void 0===t&&(t={});var n=t.decode,o=void 0===n?function(e){return e}:n;return function(t){var n=e.exec(t);if(!n)return!1;for(var i=n[0],a=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var t=r[e-1];"*"===t.modifier||"+"===t.modifier?s[t.name]=n[e].split(t.prefix+t.suffix).map(function(e){return o(e,t)}):s[t.name]=o(n[e],t)}}(l);return{path:i,index:a,params:s}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function i(e){return e&&e.sensitive?"":"i"}function a(e,r,t){void 0===t&&(t={});for(var n=t.strict,a=void 0!==n&&n,s=t.start,l=t.end,c=t.encode,u=void 0===c?function(e){return e}:c,d="["+o(t.endsWith||"")+"]|$",p="["+o(t.delimiter||"/#?")+"]",f=void 0===s||s?"^":"",m=0;m<e.length;m++){var h=e[m];if("string"==typeof h)f+=o(u(h));else{var g=o(u(h.prefix)),b=o(u(h.suffix));if(h.pattern)if(r&&r.push(h),g||b)if("+"===h.modifier||"*"===h.modifier){var v="*"===h.modifier?"?":"";f+="(?:"+g+"((?:"+h.pattern+")(?:"+b+g+"(?:"+h.pattern+"))*)"+b+")"+v}else f+="(?:"+g+"("+h.pattern+")"+b+")"+h.modifier;else f+="("+h.pattern+")"+h.modifier;else f+="(?:"+g+b+")"+h.modifier}}if(void 0===l||l)a||(f+=p+"?"),f+=t.endsWith?"(?="+d+")":"$";else{var x=e[e.length-1],y="string"==typeof x?p.indexOf(x[x.length-1])>-1:void 0===x;a||(f+="(?:"+p+"(?="+d+"))?"),y||(f+="(?="+p+"|"+d+")")}return new RegExp(f,i(t))}function s(r,t,n){if(r instanceof RegExp){if(!t)return r;var o=r.source.match(/\((?!\?)/g);if(o)for(var l=0;l<o.length;l++)t.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return r}return Array.isArray(r)?RegExp("(?:"+r.map(function(e){return s(e,t,n).source}).join("|")+")",i(n)):a(e(r,n),t,n)}Object.defineProperty(r,"__esModule",{value:!0}),r.parse=e,r.compile=function(r,n){return t(e(r,n),n)},r.tokensToFunction=t,r.match=function(e,r){var t=[];return n(s(e,t,r),t,r)},r.regexpToFunction=n,r.tokensToRegexp=a,r.pathToRegexp=s})(),e.exports=r})()},5526:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{compileNonPath:function(){return u},matchHas:function(){return c},parseDestination:function(){return d},prepareDestination:function(){return p}});let n=t(5362),o=t(3293),i=t(6759),a=t(1437),s=t(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,r,t,n){void 0===t&&(t=[]),void 0===n&&(n=[]);let o={},i=t=>{let n,i=t.key;switch(t.type){case"header":i=i.toLowerCase(),n=e.headers[i];break;case"cookie":n="cookies"in e?e.cookies[t.key]:(0,s.getCookieParser)(e.headers)()[t.key];break;case"query":n=r[i];break;case"host":{let{host:r}=(null==e?void 0:e.headers)||{};n=null==r?void 0:r.split(":",1)[0].toLowerCase()}}if(!t.value&&n)return o[function(e){let r="";for(let t=0;t<e.length;t++){let n=e.charCodeAt(t);(n>64&&n<91||n>96&&n<123)&&(r+=e[t])}return r}(i)]=n,!0;if(n){let e=RegExp("^"+t.value+"$"),r=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(r)return Array.isArray(r)&&(r.groups?Object.keys(r.groups).forEach(e=>{o[e]=r.groups[e]}):"host"===t.type&&r[0]&&(o.host=r[0])),!0}return!1};return!(!t.every(e=>i(e))||n.some(e=>i(e)))&&o}function u(e,r){if(!e.includes(":"))return e;for(let t of Object.keys(r))e.includes(":"+t)&&(e=e.replace(RegExp(":"+t+"\\*","g"),":"+t+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+t+"\\?","g"),":"+t+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+t+"\\+","g"),":"+t+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+t+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+t));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(r).slice(1)}function d(e){let r=e.destination;for(let t of Object.keys({...e.params,...e.query}))t&&(r=r.replace(RegExp(":"+(0,o.escapeStringRegexp)(t),"g"),"__ESC_COLON_"+t));let t=(0,i.parseUrl)(r),n=t.pathname;n&&(n=l(n));let a=t.href;a&&(a=l(a));let s=t.hostname;s&&(s=l(s));let c=t.hash;return c&&(c=l(c)),{...t,pathname:n,hostname:s,href:a,hash:c}}function p(e){let r,t,o=Object.assign({},e.query),i=d(e),{hostname:s,query:c}=i,p=i.pathname;i.hash&&(p=""+p+i.hash);let f=[],m=[];for(let e of((0,n.pathToRegexp)(p,m),m))f.push(e.name);if(s){let e=[];for(let r of((0,n.pathToRegexp)(s,e),e))f.push(r.name)}let h=(0,n.compile)(p,{validate:!1});for(let[t,o]of(s&&(r=(0,n.compile)(s,{validate:!1})),Object.entries(c)))Array.isArray(o)?c[t]=o.map(r=>u(l(r),e.params)):"string"==typeof o&&(c[t]=u(l(o),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>f.includes(e)))for(let r of g)r in c||(c[r]=e.params[r]);if((0,a.isInterceptionRouteAppPath)(p))for(let r of p.split("/")){let t=a.INTERCEPTION_ROUTE_MARKERS.find(e=>r.startsWith(e));if(t){"(..)(..)"===t?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=t;break}}try{let[n,o]=(t=h(e.params)).split("#",2);r&&(i.hostname=r(e.params)),i.pathname=n,i.hash=(o?"#":"")+(o||""),delete i.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return i.query={...o,...i.query},{newUrl:t,destQuery:c,parsedDestination:i}}},5531:(e,r)=>{"use strict";function t(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ensureLeadingSlash",{enumerable:!0,get:function(){return t}})},5552:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},5824:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},6341:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{getPreviouslyRevalidatedTags:function(){return b},getUtils:function(){return g},interpolateDynamicPath:function(){return m},normalizeDynamicRouteParams:function(){return h},normalizeVercelUrl:function(){return f}});let n=t(9551),o=t(1959),i=t(2437),a=t(4396),s=t(8034),l=t(5526),c=t(2887),u=t(4722),d=t(6143),p=t(7912);function f(e,r,t){let o=(0,n.parse)(e.url,!0);for(let e of(delete o.search,Object.keys(o.query))){let n=e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX),i=e!==d.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(d.NEXT_INTERCEPTION_MARKER_PREFIX);(n||i||r.includes(e)||t&&Object.keys(t.groups).includes(e))&&delete o.query[e]}e.url=(0,n.format)(o)}function m(e,r,t){if(!t)return e;for(let n of Object.keys(t.groups)){let o,{optional:i,repeat:a}=t.groups[n],s=`[${a?"...":""}${n}]`;i&&(s=`[${s}]`);let l=r[n];o=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(s,o)}return e}function h(e,r,t,n){let o={};for(let i of Object.keys(r.groups)){let a=e[i];"string"==typeof a?a=(0,u.normalizeRscURL)(a):Array.isArray(a)&&(a=a.map(u.normalizeRscURL));let s=t[i],l=r.groups[i].optional;if((Array.isArray(s)?s.some(e=>Array.isArray(a)?a.some(r=>r.includes(e)):null==a?void 0:a.includes(e)):null==a?void 0:a.includes(s))||void 0===a&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!a||Array.isArray(a)&&1===a.length&&("index"===a[0]||a[0]===`[[...${i}]]`))&&(a=void 0,delete e[i]),a&&"string"==typeof a&&r.groups[i].repeat&&(a=a.split("/")),a&&(o[i]=a)}return{params:o,hasValidParams:!0}}function g({page:e,i18n:r,basePath:t,rewrites:n,pageIsDynamic:u,trailingSlash:d,caseSensitive:g}){let b,v,x;return u&&(b=(0,a.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),x=(v=(0,s.getRouteMatcher)(b))(e)),{handleRewrites:function(a,s){let p={},f=s.pathname,m=n=>{let c=(0,i.getPathMatch)(n.source+(d?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g});if(!s.pathname)return!1;let m=c(s.pathname);if((n.has||n.missing)&&m){let e=(0,l.matchHas)(a,s.query,n.has,n.missing);e?Object.assign(m,e):m=!1}if(m){let{parsedDestination:i,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:m,query:s.query});if(i.protocol)return!0;if(Object.assign(p,a,m),Object.assign(s.query,i.query),delete i.query,Object.assign(s,i),!(f=s.pathname))return!1;if(t&&(f=f.replace(RegExp(`^${t}`),"")||"/"),r){let e=(0,o.normalizeLocalePath)(f,r.locales);f=e.pathname,s.query.nextInternalLocale=e.detectedLocale||m.nextInternalLocale}if(f===e)return!0;if(u&&v){let e=v(f);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])m(e);if(f!==e){let r=!1;for(let e of n.afterFiles||[])if(r=m(e))break;if(!r&&!(()=>{let r=(0,c.removeTrailingSlash)(f||"");return r===(0,c.removeTrailingSlash)(e)||(null==v?void 0:v(r))})()){for(let e of n.fallback||[])if(r=m(e))break}}return p},defaultRouteRegex:b,dynamicRouteMatcher:v,defaultRouteMatches:x,getParamsFromRouteMatches:function(e){if(!b)return null;let{groups:r,routeKeys:t}=b,n=(0,s.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,r]of Object.entries(n)){let t=(0,p.normalizeNextQueryParam)(e);t&&(n[t]=r,delete n[e])}let o={};for(let e of Object.keys(t)){let i=t[e];if(!i)continue;let a=r[i],s=n[e];if(!a.optional&&!s)return null;o[a.pos]=s}return o}},groups:r})(e);return n||null},normalizeDynamicRouteParams:(e,r)=>b&&x?h(e,b,x,r):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,r)=>f(e,r,b),interpolateDynamicPath:(e,r)=>m(e,r,b)}}function b(e,r){return"string"==typeof e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[d.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===r?e[d.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var r={};(()=>{r.parse=function(r,t){if("string"!=typeof r)throw TypeError("argument str must be a string");for(var o={},i=r.split(n),a=(t||{}).decode||e,s=0;s<i.length;s++){var l=i[s],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==o[u]&&(o[u]=function(e,r){try{return r(e)}catch(r){return e}}(d,a))}}return o},r.serialize=function(e,r,n){var i=n||{},a=i.encode||t;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!o.test(e))throw TypeError("argument name is invalid");var s=a(r);if(s&&!o.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=i.maxAge){var c=i.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(i.domain){if(!o.test(i.domain))throw TypeError("option domain is invalid");l+="; Domain="+i.domain}if(i.path){if(!o.test(i.path))throw TypeError("option path is invalid");l+="; Path="+i.path}if(i.expires){if("function"!=typeof i.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(l+="; HttpOnly"),i.secure&&(l+="; Secure"),i.sameSite)switch("string"==typeof i.sameSite?i.sameSite.toLowerCase():i.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,t=encodeURIComponent,n=/; */,o=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=r})()},6440:()=>{},6759:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"parseUrl",{enumerable:!0,get:function(){return i}});let n=t(2785),o=t(3736);function i(e){if(e.startsWith("/"))return(0,o.parseRelativeUrl)(e);let r=new URL(e);return{hash:r.hash,hostname:r.hostname,href:r.href,pathname:r.pathname,port:r.port,protocol:r.protocol,query:(0,n.searchParamsToUrlQuery)(r.searchParams),search:r.search}}},7112:()=>{},8034:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=t(4827);function o(e){let{re:r,groups:t}=e;return e=>{let o=r.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,r]of Object.entries(t)){let t=o[r.pos];void 0!==t&&(r.repeat?a[e]=t.split("/").map(e=>i(e)):a[e]=i(t))}return a}}},8212:(e,r,t)=>{"use strict";function n(e){return function(){let{cookie:r}=e;if(!r)return{};let{parse:n}=t(6415);return n(Array.isArray(r)?r.join("; "):r)}}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"getCookieParser",{enumerable:!0,get:function(){return n}})},8297:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>c});var n=t(5239),o=t(8088),i=t(8170),a=t.n(i),s=t(893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);t.d(r,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1204)),"/Users/<USER>/Desktop/i2i/rag-project/frontend/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Desktop/i2i/rag-project/frontend/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=["/Users/<USER>/Desktop/i2i/rag-project/frontend/src/app/page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8304:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return s},STATIC_METADATA_IMAGES:function(){return a},getExtensionRegexString:function(){return l},isMetadataPage:function(){return d},isMetadataRoute:function(){return p},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return u}});let n=t(2958),o=t(4722),i=t(554),a={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},s=["js","jsx","ts","tsx"],l=(e,r)=>r&&0!==r.length?`(?:\\.(${e.join("|")})|(\\.(${r.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,r,t){let o=(t?"":"?")+"$",i=`\\d?${t?"":"(-\\w{6})?"}`,s=[RegExp(`^[\\\\/]robots${l(r.concat("txt"),null)}${o}`),RegExp(`^[\\\\/]manifest${l(r.concat("webmanifest","json"),null)}${o}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],r)}${o}`),RegExp(`[\\\\/]${a.icon.filename}${i}${l(a.icon.extensions,r)}${o}`),RegExp(`[\\\\/]${a.apple.filename}${i}${l(a.apple.extensions,r)}${o}`),RegExp(`[\\\\/]${a.openGraph.filename}${i}${l(a.openGraph.extensions,r)}${o}`),RegExp(`[\\\\/]${a.twitter.filename}${i}${l(a.twitter.extensions,r)}${o}`)],c=(0,n.normalizePathSep)(e);return s.some(e=>e.test(c))}function u(e){let r=e.replace(/\/route$/,"");return(0,i.isAppRouteRoute)(e)&&c(r,[],!0)&&"/robots.txt"!==r&&"/manifest.webmanifest"!==r&&!r.endsWith("/sitemap.xml")}function d(e){return!(0,i.isAppRouteRoute)(e)&&c(e,[],!1)}function p(e){let r=(0,o.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==r[0]&&(r="/"+r),(0,i.isAppRouteRoute)(e)&&c(r,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[447,169],()=>t(8297));module.exports=n})();