{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/i2i/rag-project/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/i2i/rag-project/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/i2i/rag-project/frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction ScrollArea({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\n  return (\n    <ScrollAreaPrimitive.Root\n      data-slot=\"scroll-area\"\n      className={cn(\"relative\", className)}\n      {...props}\n    >\n      <ScrollAreaPrimitive.Viewport\n        data-slot=\"scroll-area-viewport\"\n        className=\"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1\"\n      >\n        {children}\n      </ScrollAreaPrimitive.Viewport>\n      <ScrollBar />\n      <ScrollAreaPrimitive.Corner />\n    </ScrollAreaPrimitive.Root>\n  )\n}\n\nfunction ScrollBar({\n  className,\n  orientation = \"vertical\",\n  ...props\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\n  return (\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\n      data-slot=\"scroll-area-scrollbar\"\n      orientation={orientation}\n      className={cn(\n        \"flex touch-none p-px transition-colors select-none\",\n        orientation === \"vertical\" &&\n          \"h-full w-2.5 border-l border-l-transparent\",\n        orientation === \"horizontal\" &&\n          \"h-2.5 flex-col border-t border-t-transparent\",\n        className\n      )}\n      {...props}\n    >\n      <ScrollAreaPrimitive.ScrollAreaThumb\n        data-slot=\"scroll-area-thumb\"\n        className=\"bg-border relative flex-1 rounded-full\"\n      />\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\n  )\n}\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,6KAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KArBS;AAuBT,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/i2i/rag-project/frontend/src/components/chat/ChatInterface.tsx"], "sourcesContent": ["\"use client\";\n\nimport type React from \"react\";\n\nimport { useState, useEffect, useRef } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { MessageCircle, Loader2, Send, Bot, User } from \"lucide-react\";\n\ninterface Message {\n  id: string;\n  role: \"user\" | \"assistant\";\n  content: string;\n  timestamp: Date;\n}\n\n// Mock chat service - replace with your actual implementation\nconst ChatService = {\n  async sendMessage({\n    question,\n    history,\n  }: {\n    question: string;\n    history: any[];\n  }) {\n    // Simulate API delay\n    await new Promise((resolve) =>\n      setTimeout(resolve, 1000 + Math.random() * 2000)\n    );\n\n    // Mock response - replace with your actual API call\n    return {\n      answer: `This is a mock response to: \"${question}\". In a real implementation, this would be connected to your RAG system.`,\n    };\n  },\n};\n\nexport default function ChatPage() {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [inputMessage, setInputMessage] = useState(\"\");\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\n\n  // Auto-scroll to bottom when new messages are added\n  useEffect(() => {\n    if (scrollAreaRef.current) {\n      const scrollContainer = scrollAreaRef.current.querySelector(\n        \"[data-radix-scroll-area-viewport]\"\n      );\n      if (scrollContainer) {\n        scrollContainer.scrollTop = scrollContainer.scrollHeight;\n      }\n    }\n  }, [messages]);\n\n  const handleSendMessage = async (content: string) => {\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      role: \"user\",\n      content,\n      timestamp: new Date(),\n    };\n\n    setMessages((prev) => [...prev, userMessage]);\n    setIsLoading(true);\n\n    try {\n      // Prepare history for the API call\n      const history = messages.map((msg) => ({\n        role: msg.role,\n        content: msg.content,\n      }));\n\n      const response = await ChatService.sendMessage({\n        question: content,\n        history,\n      });\n\n      const assistantMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        role: \"assistant\",\n        content: response.answer,\n        timestamp: new Date(),\n      };\n\n      setMessages((prev) => [...prev, assistantMessage]);\n    } catch (error) {\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        role: \"assistant\",\n        content:\n          \"Sorry, I encountered an error while processing your request. Please try again.\",\n        timestamp: new Date(),\n      };\n\n      setMessages((prev) => [...prev, errorMessage]);\n      console.error(\"Chat error:\", error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleSubmit = () => {\n    if (inputMessage.trim() && !isLoading) {\n      handleSendMessage(inputMessage.trim());\n      setInputMessage(\"\");\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\" && !e.shiftKey) {\n      e.preventDefault();\n      handleSubmit();\n    }\n  };\n\n  return (\n    <div className=\"h-screen flex flex-col bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white border-b p-4\">\n        <div className=\"max-w-4xl mx-auto flex items-center gap-2\">\n          <MessageCircle size={24} className=\"text-blue-600\" />\n          <h1 className=\"text-xl font-semibold text-gray-900\">\n            RAG Chat Assistant\n          </h1>\n        </div>\n      </div>\n\n      {/* Messages Area */}\n      <div className=\"flex-1 flex flex-col min-h-0\">\n        <ScrollArea className=\"flex-1 p-4\" ref={scrollAreaRef}>\n          <div className=\"max-w-4xl mx-auto\">\n            {messages.length === 0 ? (\n              <div className=\"flex items-center justify-center h-full text-gray-500\">\n                <div className=\"text-center\">\n                  <MessageCircle\n                    size={48}\n                    className=\"mx-auto mb-4 opacity-50\"\n                  />\n                  <p>Start a conversation by asking a question!</p>\n                </div>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {messages.map((message) => (\n                  <div\n                    key={message.id}\n                    className={`flex gap-3 ${message.role === \"user\" ? \"justify-end\" : \"justify-start\"}`}\n                  >\n                    {message.role === \"assistant\" && (\n                      <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <Bot size={16} className=\"text-blue-600\" />\n                      </div>\n                    )}\n\n                    <div\n                      className={`max-w-[70%] ${message.role === \"user\" ? \"order-first\" : \"\"}`}\n                    >\n                      <div\n                        className={`rounded-lg px-4 py-2 ${\n                          message.role === \"user\"\n                            ? \"bg-blue-600 text-white ml-auto\"\n                            : \"bg-white border text-gray-900\"\n                        }`}\n                      >\n                        <p className=\"text-sm whitespace-pre-wrap\">\n                          {message.content}\n                        </p>\n                      </div>\n                      <div\n                        className={`text-xs text-gray-500 mt-1 ${message.role === \"user\" ? \"text-right\" : \"text-left\"}`}\n                      >\n                        {message.timestamp.toLocaleTimeString([], {\n                          hour: \"2-digit\",\n                          minute: \"2-digit\",\n                        })}\n                      </div>\n                    </div>\n\n                    {message.role === \"user\" && (\n                      <div className=\"w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0\">\n                        <User size={16} className=\"text-white\" />\n                      </div>\n                    )}\n                  </div>\n                ))}\n\n                {isLoading && (\n                  <div className=\"flex justify-start mb-4\">\n                    <div className=\"flex items-center gap-2 bg-gray-100 rounded-lg p-3\">\n                      <Loader2 size={16} className=\"animate-spin\" />\n                      <span className=\"text-sm text-gray-600\">Thinking...</span>\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </ScrollArea>\n\n        {/* Input Area */}\n        <div className=\"border-t bg-white p-4\">\n          <div className=\"max-w-4xl mx-auto flex gap-2\">\n            <Textarea\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Type your message...\"\n              disabled={isLoading}\n              className=\"flex-1 min-h-[44px] max-h-32 resize-none\"\n              rows={1}\n            />\n            <Button\n              onClick={handleSubmit}\n              disabled={!inputMessage.trim() || isLoading}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              <Send size={16} />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;AAiBA,8DAA8D;AAC9D,MAAM,cAAc;IAClB,MAAM,aAAY,EAChB,QAAQ,EACR,OAAO,EAIR;QACC,qBAAqB;QACrB,MAAM,IAAI,QAAQ,CAAC,UACjB,WAAW,SAAS,OAAO,KAAK,MAAM,KAAK;QAG7C,oDAAoD;QACpD,OAAO;YACL,QAAQ,CAAC,6BAA6B,EAAE,SAAS,wEAAwE,CAAC;QAC5H;IACF;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE7C,oDAAoD;IACpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,cAAc,OAAO,EAAE;gBACzB,MAAM,kBAAkB,cAAc,OAAO,CAAC,aAAa,CACzD;gBAEF,IAAI,iBAAiB;oBACnB,gBAAgB,SAAS,GAAG,gBAAgB,YAAY;gBAC1D;YACF;QACF;6BAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,OAAO;QAC/B,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN;YACA,WAAW,IAAI;QACjB;QAEA,YAAY,CAAC,OAAS;mBAAI;gBAAM;aAAY;QAC5C,aAAa;QAEb,IAAI;YACF,mCAAmC;YACnC,MAAM,UAAU,SAAS,GAAG,CAAC,CAAC,MAAQ,CAAC;oBACrC,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;gBACtB,CAAC;YAED,MAAM,WAAW,MAAM,YAAY,WAAW,CAAC;gBAC7C,UAAU;gBACV;YACF;YAEA,MAAM,mBAA4B;gBAChC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,SAAS,MAAM;gBACxB,WAAW,IAAI;YACjB;YAEA,YAAY,CAAC,OAAS;uBAAI;oBAAM;iBAAiB;QACnD,EAAE,OAAO,OAAO;YACd,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SACE;gBACF,WAAW,IAAI;YACjB;YAEA,YAAY,CAAC,OAAS;uBAAI;oBAAM;iBAAa;YAC7C,QAAQ,KAAK,CAAC,eAAe;QAC/B,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,aAAa,IAAI,MAAM,CAAC,WAAW;YACrC,kBAAkB,aAAa,IAAI;YACnC,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCACnC,6LAAC;4BAAG,WAAU;sCAAsC;;;;;;;;;;;;;;;;;0BAOxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;wBAAa,KAAK;kCACtC,cAAA,6LAAC;4BAAI,WAAU;sCACZ,SAAS,MAAM,KAAK,kBACnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CACZ,MAAM;4CACN,WAAU;;;;;;sDAEZ,6LAAC;sDAAE;;;;;;;;;;;;;;;;qDAIP,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;4CAEC,WAAW,CAAC,WAAW,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;;gDAEnF,QAAQ,IAAI,KAAK,6BAChB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,MAAM;wDAAI,WAAU;;;;;;;;;;;8DAI7B,6LAAC;oDACC,WAAW,CAAC,YAAY,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,IAAI;;sEAExE,6LAAC;4DACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,IAAI,KAAK,SACb,mCACA,iCACJ;sEAEF,cAAA,6LAAC;gEAAE,WAAU;0EACV,QAAQ,OAAO;;;;;;;;;;;sEAGpB,6LAAC;4DACC,WAAW,CAAC,2BAA2B,EAAE,QAAQ,IAAI,KAAK,SAAS,eAAe,aAAa;sEAE9F,QAAQ,SAAS,CAAC,kBAAkB,CAAC,EAAE,EAAE;gEACxC,MAAM;gEACN,QAAQ;4DACV;;;;;;;;;;;;gDAIH,QAAQ,IAAI,KAAK,wBAChB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,MAAM;wDAAI,WAAU;;;;;;;;;;;;2CAnCzB,QAAQ,EAAE;;;;;oCAyClB,2BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oNAAA,CAAA,UAAO;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC7B,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUtD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,YAAY;oCACZ,aAAY;oCACZ,UAAU;oCACV,WAAU;oCACV,MAAM;;;;;;8CAER,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,aAAa,IAAI,MAAM;oCAClC,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1B;GA3LwB;KAAA", "debugId": null}}]}