"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[521],{1007:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1154:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1366:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1414:(e,r,t)=>{e.exports=t(2436)},2085:(e,r,t)=>{t.d(r,{F:()=>a});var o=t(2596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=o.$,a=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:i}=r,s=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],o=null==i?void 0:i[e];if(null===r)return null;let l=n(r)||n(o);return a[e][l]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return l(e,s,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},2436:(e,r,t)=>{var o=t(2115),n="function"==typeof Object.is?Object.is:function(e,r){return e===r&&(0!==e||1/e==1/r)||e!=e&&r!=r},l=o.useState,a=o.useEffect,i=o.useLayoutEffect,s=o.useDebugValue;function d(e){var r=e.getSnapshot;e=e.value;try{var t=r();return!n(e,t)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,r){return r()}:function(e,r){var t=r(),o=l({inst:{value:t,getSnapshot:r}}),n=o[0].inst,c=o[1];return i(function(){n.value=t,n.getSnapshot=r,d(n)&&c({inst:n})},[e,t,r]),a(function(){return d(n)&&c({inst:n}),e(function(){d(n)&&c({inst:n})})},[e]),s(t),t};r.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:c},2486:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2596:(e,r,t)=>{t.d(r,{$:()=>o});function o(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var l=r.length;for(t=0;t<l;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}},2712:(e,r,t)=>{t.d(r,{N:()=>n});var o=t(2115),n=globalThis?.document?o.useLayoutEffect:()=>{}},3655:(e,r,t)=>{t.d(r,{sG:()=>a});var o=t(2115);t(7650);var n=t(9708),l=t(5155),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),a=o.forwardRef((e,o)=>{let{asChild:n,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(n?t:r,{...a,ref:o})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{})},4011:(e,r,t)=>{t.d(r,{H4:()=>k,bL:()=>x});var o=t(2115),n=t(6081),l=t(9033),a=t(2712),i=t(3655),s=t(1414);function d(){return()=>{}}var c=t(5155),u="Avatar",[p,m]=(0,n.A)(u),[f,b]=p(u),g=o.forwardRef((e,r)=>{let{__scopeAvatar:t,...n}=e,[l,a]=o.useState("idle");return(0,c.jsx)(f,{scope:t,imageLoadingStatus:l,onImageLoadingStatusChange:a,children:(0,c.jsx)(i.sG.span,{...n,ref:r})})});g.displayName=u;var h="AvatarImage";o.forwardRef((e,r)=>{let{__scopeAvatar:t,src:n,onLoadingStatusChange:u=()=>{},...p}=e,m=b(h,t),f=function(e,r){let{referrerPolicy:t,crossOrigin:n}=r,l=(0,s.useSyncExternalStore)(d,()=>!0,()=>!1),i=o.useRef(null),c=l?(i.current||(i.current=new window.Image),i.current):null,[u,p]=o.useState(()=>y(c,e));return(0,a.N)(()=>{p(y(c,e))},[c,e]),(0,a.N)(()=>{let e=e=>()=>{p(e)};if(!c)return;let r=e("loaded"),o=e("error");return c.addEventListener("load",r),c.addEventListener("error",o),t&&(c.referrerPolicy=t),"string"==typeof n&&(c.crossOrigin=n),()=>{c.removeEventListener("load",r),c.removeEventListener("error",o)}},[c,n,t]),u}(n,p),g=(0,l.c)(e=>{u(e),m.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==f&&g(f)},[f,g]),"loaded"===f?(0,c.jsx)(i.sG.img,{...p,ref:r,src:n}):null}).displayName=h;var v="AvatarFallback",w=o.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:n,...l}=e,a=b(v,t),[s,d]=o.useState(void 0===n);return o.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>d(!0),n);return()=>window.clearTimeout(e)}},[n]),s&&"loaded"!==a.imageLoadingStatus?(0,c.jsx)(i.sG.span,{...l,ref:r}):null});function y(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=v;var x=g,k=w},5657:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},6081:(e,r,t)=>{t.d(r,{A:()=>l});var o=t(2115),n=t(5155);function l(e,r=[]){let t=[],a=()=>{let r=t.map(e=>o.createContext(e));return function(t){let n=t?.[e]||r;return o.useMemo(()=>({[`__scope${e}`]:{...t,[e]:n}}),[t,n])}};return a.scopeName=e,[function(r,l){let a=o.createContext(l),i=t.length;t=[...t,l];let s=r=>{let{scope:t,children:l,...s}=r,d=t?.[e]?.[i]||a,c=o.useMemo(()=>s,Object.values(s));return(0,n.jsx)(d.Provider,{value:c,children:l})};return s.displayName=r+"Provider",[s,function(t,n){let s=n?.[e]?.[i]||a,d=o.useContext(s);if(d)return d;if(void 0!==l)return l;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=t.reduce((r,{useScope:t,scopeName:o})=>{let n=t(e)[`__scope${o}`];return{...r,...n}},{});return o.useMemo(()=>({[`__scope${r.scopeName}`]:n}),[n])}};return t.scopeName=r.scopeName,t}(a,...r)]}},6101:(e,r,t)=>{t.d(r,{s:()=>a,t:()=>l});var o=t(2115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function l(...e){return r=>{let t=!1,o=e.map(e=>{let o=n(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():n(e[r],null)}}}}function a(...e){return o.useCallback(l(...e),e)}},7926:(e,r,t)=>{t.d(r,{OK:()=>q,bL:()=>Y,VM:()=>S,lr:()=>M,LM:()=>B});var o=t(2115),n=t(3655),l=t(6101),a=t(2712),i=e=>{let{present:r,children:t}=e,n=function(e){var r,t;let[n,l]=o.useState(),i=o.useRef(null),d=o.useRef(e),c=o.useRef("none"),[u,p]=(r=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,r)=>{let o=t[e][r];return null!=o?o:e},r));return o.useEffect(()=>{let e=s(i.current);c.current="mounted"===u?e:"none"},[u]),(0,a.N)(()=>{let r=i.current,t=d.current;if(t!==e){let o=c.current,n=s(r);e?p("MOUNT"):"none"===n||(null==r?void 0:r.display)==="none"?p("UNMOUNT"):t&&o!==n?p("ANIMATION_OUT"):p("UNMOUNT"),d.current=e}},[e,p]),(0,a.N)(()=>{if(n){var e;let r,t=null!=(e=n.ownerDocument.defaultView)?e:window,o=e=>{let o=s(i.current).includes(e.animationName);if(e.target===n&&o&&(p("ANIMATION_END"),!d.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",r=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},l=e=>{e.target===n&&(c.current=s(i.current))};return n.addEventListener("animationstart",l),n.addEventListener("animationcancel",o),n.addEventListener("animationend",o),()=>{t.clearTimeout(r),n.removeEventListener("animationstart",l),n.removeEventListener("animationcancel",o),n.removeEventListener("animationend",o)}}p("ANIMATION_END")},[n,p]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(r),i="function"==typeof t?t({present:n.isPresent}):o.Children.only(t),d=(0,l.s)(n.ref,function(e){var r,t;let o=null==(r=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:r.get,n=o&&"isReactWarning"in o&&o.isReactWarning;return n?e.ref:(n=(o=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof t||n.isPresent?o.cloneElement(i,{ref:d}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence";var d=t(6081),c=t(9033),u=t(5155),p=o.createContext(void 0);function m(e,r,{checkForDefaultPrevented:t=!0}={}){return function(o){if(e?.(o),!1===t||!o.defaultPrevented)return r?.(o)}}var f="ScrollArea",[b,g]=(0,d.A)(f),[h,v]=b(f),w=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,type:a="hover",dir:i,scrollHideDelay:s=600,...d}=e,[c,m]=o.useState(null),[f,b]=o.useState(null),[g,v]=o.useState(null),[w,y]=o.useState(null),[x,k]=o.useState(null),[S,E]=o.useState(0),[N,C]=o.useState(0),[z,T]=o.useState(!1),[j,R]=o.useState(!1),A=(0,l.s)(r,e=>m(e)),L=function(e){let r=o.useContext(p);return e||r||"ltr"}(i);return(0,u.jsx)(h,{scope:t,type:a,dir:L,scrollHideDelay:s,scrollArea:c,viewport:f,onViewportChange:b,content:g,onContentChange:v,scrollbarX:w,onScrollbarXChange:y,scrollbarXEnabled:z,onScrollbarXEnabledChange:T,scrollbarY:x,onScrollbarYChange:k,scrollbarYEnabled:j,onScrollbarYEnabledChange:R,onCornerWidthChange:E,onCornerHeightChange:C,children:(0,u.jsx)(n.sG.div,{dir:L,...d,ref:A,style:{position:"relative","--radix-scroll-area-corner-width":S+"px","--radix-scroll-area-corner-height":N+"px",...e.style}})})});w.displayName=f;var y="ScrollAreaViewport",x=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,children:a,nonce:i,...s}=e,d=v(y,t),c=o.useRef(null),p=(0,l.s)(r,c,d.onViewportChange);return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:i}),(0,u.jsx)(n.sG.div,{"data-radix-scroll-area-viewport":"",...s,ref:p,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,u.jsx)("div",{ref:d.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});x.displayName=y;var k="ScrollAreaScrollbar",S=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=v(k,e.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:i}=l,s="horizontal"===e.orientation;return o.useEffect(()=>(s?a(!0):i(!0),()=>{s?a(!1):i(!1)}),[s,a,i]),"hover"===l.type?(0,u.jsx)(E,{...n,ref:r,forceMount:t}):"scroll"===l.type?(0,u.jsx)(N,{...n,ref:r,forceMount:t}):"auto"===l.type?(0,u.jsx)(C,{...n,ref:r,forceMount:t}):"always"===l.type?(0,u.jsx)(z,{...n,ref:r}):null});S.displayName=k;var E=o.forwardRef((e,r)=>{let{forceMount:t,...n}=e,l=v(k,e.__scopeScrollArea),[a,s]=o.useState(!1);return o.useEffect(()=>{let e=l.scrollArea,r=0;if(e){let t=()=>{window.clearTimeout(r),s(!0)},o=()=>{r=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",t),e.addEventListener("pointerleave",o),()=>{window.clearTimeout(r),e.removeEventListener("pointerenter",t),e.removeEventListener("pointerleave",o)}}},[l.scrollArea,l.scrollHideDelay]),(0,u.jsx)(i,{present:t||a,children:(0,u.jsx)(C,{"data-state":a?"visible":"hidden",...n,ref:r})})}),N=o.forwardRef((e,r)=>{var t,n;let{forceMount:l,...a}=e,s=v(k,e.__scopeScrollArea),d="horizontal"===e.orientation,c=F(()=>f("SCROLL_END"),100),[p,f]=(t="hidden",n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},o.useReducer((e,r)=>{let t=n[e][r];return null!=t?t:e},t));return o.useEffect(()=>{if("idle"===p){let e=window.setTimeout(()=>f("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[p,s.scrollHideDelay,f]),o.useEffect(()=>{let e=s.viewport,r=d?"scrollLeft":"scrollTop";if(e){let t=e[r],o=()=>{let o=e[r];t!==o&&(f("SCROLL"),c()),t=o};return e.addEventListener("scroll",o),()=>e.removeEventListener("scroll",o)}},[s.viewport,d,f,c]),(0,u.jsx)(i,{present:l||"hidden"!==p,children:(0,u.jsx)(z,{"data-state":"hidden"===p?"hidden":"visible",...a,ref:r,onPointerEnter:m(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:m(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),C=o.forwardRef((e,r)=>{let t=v(k,e.__scopeScrollArea),{forceMount:n,...l}=e,[a,s]=o.useState(!1),d="horizontal"===e.orientation,c=F(()=>{if(t.viewport){let e=t.viewport.offsetWidth<t.viewport.scrollWidth,r=t.viewport.offsetHeight<t.viewport.scrollHeight;s(d?e:r)}},10);return X(t.viewport,c),X(t.content,c),(0,u.jsx)(i,{present:n||a,children:(0,u.jsx)(z,{"data-state":a?"visible":"hidden",...l,ref:r})})}),z=o.forwardRef((e,r)=>{let{orientation:t="vertical",...n}=e,l=v(k,e.__scopeScrollArea),a=o.useRef(null),i=o.useRef(0),[s,d]=o.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),c=U(s.viewport,s.content),p={...n,sizes:s,onSizesChange:d,hasThumb:!!(c>0&&c<1),onThumbChange:e=>a.current=e,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:e=>i.current=e};function m(e,r){return function(e,r,t){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",n=$(t),l=r||n/2,a=t.scrollbar.paddingStart+l,i=t.scrollbar.size-t.scrollbar.paddingEnd-(n-l),s=t.content-t.viewport;return H([a,i],"ltr"===o?[0,s]:[-1*s,0])(e)}(e,i.current,s,r)}return"horizontal"===t?(0,u.jsx)(T,{...p,ref:r,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=G(l.viewport.scrollLeft,s,l.dir);a.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=m(e,l.dir))}}):"vertical"===t?(0,u.jsx)(j,{...p,ref:r,onThumbPositionChange:()=>{if(l.viewport&&a.current){let e=G(l.viewport.scrollTop,s);a.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=m(e))}}):null}),T=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...a}=e,i=v(k,e.__scopeScrollArea),[s,d]=o.useState(),c=o.useRef(null),p=(0,l.s)(r,c,i.onScrollbarXChange);return o.useEffect(()=>{c.current&&d(getComputedStyle(c.current))},[c]),(0,u.jsx)(L,{"data-orientation":"horizontal",...a,ref:p,sizes:t,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":$(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.x),onDragScroll:r=>e.onDragScroll(r.x),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollLeft+r.deltaX;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&n({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:c.current.clientWidth,paddingStart:W(s.paddingLeft),paddingEnd:W(s.paddingRight)}})}})}),j=o.forwardRef((e,r)=>{let{sizes:t,onSizesChange:n,...a}=e,i=v(k,e.__scopeScrollArea),[s,d]=o.useState(),c=o.useRef(null),p=(0,l.s)(r,c,i.onScrollbarYChange);return o.useEffect(()=>{c.current&&d(getComputedStyle(c.current))},[c]),(0,u.jsx)(L,{"data-orientation":"vertical",...a,ref:p,sizes:t,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":$(t)+"px",...e.style},onThumbPointerDown:r=>e.onThumbPointerDown(r.y),onDragScroll:r=>e.onDragScroll(r.y),onWheelScroll:(r,t)=>{if(i.viewport){let o=i.viewport.scrollTop+r.deltaY;e.onWheelScroll(o),function(e,r){return e>0&&e<r}(o,t)&&r.preventDefault()}},onResize:()=>{c.current&&i.viewport&&s&&n({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:c.current.clientHeight,paddingStart:W(s.paddingTop),paddingEnd:W(s.paddingBottom)}})}})}),[R,A]=b(k),L=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,sizes:a,hasThumb:i,onThumbChange:s,onThumbPointerUp:d,onThumbPointerDown:p,onThumbPositionChange:f,onDragScroll:b,onWheelScroll:g,onResize:h,...w}=e,y=v(k,t),[x,S]=o.useState(null),E=(0,l.s)(r,e=>S(e)),N=o.useRef(null),C=o.useRef(""),z=y.viewport,T=a.content-a.viewport,j=(0,c.c)(g),A=(0,c.c)(f),L=F(h,10);function P(e){N.current&&b({x:e.clientX-N.current.left,y:e.clientY-N.current.top})}return o.useEffect(()=>{let e=e=>{let r=e.target;(null==x?void 0:x.contains(r))&&j(e,T)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[z,x,T,j]),o.useEffect(A,[a,A]),X(x,L),X(y.content,L),(0,u.jsx)(R,{scope:t,scrollbar:x,hasThumb:i,onThumbChange:(0,c.c)(s),onThumbPointerUp:(0,c.c)(d),onThumbPositionChange:A,onThumbPointerDown:(0,c.c)(p),children:(0,u.jsx)(n.sG.div,{...w,ref:E,style:{position:"absolute",...w.style},onPointerDown:m(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),N.current=x.getBoundingClientRect(),C.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),P(e))}),onPointerMove:m(e.onPointerMove,P),onPointerUp:m(e.onPointerUp,e=>{let r=e.target;r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=C.current,y.viewport&&(y.viewport.style.scrollBehavior=""),N.current=null})})})}),P="ScrollAreaThumb",M=o.forwardRef((e,r)=>{let{forceMount:t,...o}=e,n=A(P,e.__scopeScrollArea);return(0,u.jsx)(i,{present:t||n.hasThumb,children:(0,u.jsx)(_,{ref:r,...o})})}),_=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,style:a,...i}=e,s=v(P,t),d=A(P,t),{onThumbPositionChange:c}=d,p=(0,l.s)(r,e=>d.onThumbChange(e)),f=o.useRef(void 0),b=F(()=>{f.current&&(f.current(),f.current=void 0)},100);return o.useEffect(()=>{let e=s.viewport;if(e){let r=()=>{b(),f.current||(f.current=V(e,c),c())};return c(),e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[s.viewport,b,c]),(0,u.jsx)(n.sG.div,{"data-state":d.hasThumb?"visible":"hidden",...i,ref:p,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:m(e.onPointerDownCapture,e=>{let r=e.target.getBoundingClientRect(),t=e.clientX-r.left,o=e.clientY-r.top;d.onThumbPointerDown({x:t,y:o})}),onPointerUp:m(e.onPointerUp,d.onThumbPointerUp)})});M.displayName=P;var O="ScrollAreaCorner",D=o.forwardRef((e,r)=>{let t=v(O,e.__scopeScrollArea),o=!!(t.scrollbarX&&t.scrollbarY);return"scroll"!==t.type&&o?(0,u.jsx)(I,{...e,ref:r}):null});D.displayName=O;var I=o.forwardRef((e,r)=>{let{__scopeScrollArea:t,...l}=e,a=v(O,t),[i,s]=o.useState(0),[d,c]=o.useState(0),p=!!(i&&d);return X(a.scrollbarX,()=>{var e;let r=(null==(e=a.scrollbarX)?void 0:e.offsetHeight)||0;a.onCornerHeightChange(r),c(r)}),X(a.scrollbarY,()=>{var e;let r=(null==(e=a.scrollbarY)?void 0:e.offsetWidth)||0;a.onCornerWidthChange(r),s(r)}),p?(0,u.jsx)(n.sG.div,{...l,ref:r,style:{width:i,height:d,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function W(e){return e?parseInt(e,10):0}function U(e,r){let t=e/r;return isNaN(t)?0:t}function $(e){let r=U(e.viewport,e.content),t=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-t)*r,18)}function G(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",o=$(r),n=r.scrollbar.paddingStart+r.scrollbar.paddingEnd,l=r.scrollbar.size-n,a=r.content-r.viewport,i=function(e,[r,t]){return Math.min(t,Math.max(r,e))}(e,"ltr"===t?[0,a]:[-1*a,0]);return H([0,a],[0,l-o])(i)}function H(e,r){return t=>{if(e[0]===e[1]||r[0]===r[1])return r[0];let o=(r[1]-r[0])/(e[1]-e[0]);return r[0]+o*(t-e[0])}}var V=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},t={left:e.scrollLeft,top:e.scrollTop},o=0;return!function n(){let l={left:e.scrollLeft,top:e.scrollTop},a=t.left!==l.left,i=t.top!==l.top;(a||i)&&r(),t=l,o=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(o)};function F(e,r){let t=(0,c.c)(e),n=o.useRef(0);return o.useEffect(()=>()=>window.clearTimeout(n.current),[]),o.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(t,r)},[t,r])}function X(e,r){let t=(0,c.c)(r);(0,a.N)(()=>{let r=0;if(e){let o=new ResizeObserver(()=>{cancelAnimationFrame(r),r=window.requestAnimationFrame(t)});return o.observe(e),()=>{window.cancelAnimationFrame(r),o.unobserve(e)}}},[e,t])}var Y=w,B=x,q=D},9033:(e,r,t)=>{t.d(r,{c:()=>n});var o=t(2115);function n(e){let r=o.useRef(e);return o.useEffect(()=>{r.current=e}),o.useMemo(()=>(...e)=>r.current?.(...e),[])}},9688:(e,r,t)=>{t.d(r,{QP:()=>ed});let o=e=>{let r=i(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),n(t,r)||a(e)},getConflictingClassGroupIds:(e,r)=>{let n=t[e]||[];return r&&o[e]?[...n,...o[e]]:n}}},n=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),l=o?n(e.slice(1),o):void 0;if(l)return l;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},l=/^\[(.+)\]$/,a=e=>{if(l.test(e)){let r=l.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},i=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)s(t[e],o,e,r);return o},s=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:d(r,e)).classGroupId=t;return}if("function"==typeof e)return c(e)?void s(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{s(n,d(r,e),t,o)})})},d=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},c=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,l)=>{t.set(n,l),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}},p=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,n=0,l=0;for(let a=0;a<e.length;a++){let i=e[a];if(0===o&&0===n){if(":"===i){t.push(e.slice(l,a)),l=a+1;continue}if("/"===i){r=a;continue}}"["===i?o++:"]"===i?o--:"("===i?n++:")"===i&&n--}let a=0===t.length?e:e.substring(l),i=m(a);return{modifiers:t,hasImportantModifier:i!==a,baseClassName:i,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},m=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,f=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},b=e=>({cache:u(e.cacheSize),parseClassName:p(e),sortModifiers:f(e),...o(e)}),g=/\s+/,h=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:l}=r,a=[],i=e.trim().split(g),s="";for(let e=i.length-1;e>=0;e-=1){let r=i[e],{isExternal:d,modifiers:c,hasImportantModifier:u,baseClassName:p,maybePostfixModifierPosition:m}=t(r);if(d){s=r+(s.length>0?" "+s:s);continue}let f=!!m,b=o(f?p.substring(0,m):p);if(!b){if(!f||!(b=o(p))){s=r+(s.length>0?" "+s:s);continue}f=!1}let g=l(c).join(":"),h=u?g+"!":g,v=h+b;if(a.includes(v))continue;a.push(v);let w=n(b,f);for(let e=0;e<w.length;++e){let r=w[e];a.push(h+r)}s=r+(s.length>0?" "+s:s)}return s};function v(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=w(e))&&(o&&(o+=" "),o+=r);return o}let w=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=w(e[o]))&&(t&&(t+=" "),t+=r);return t},y=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,S=/^\d+\/\d+$/,E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,N=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,C=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,z=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,T=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,j=e=>S.test(e),R=e=>!!e&&!Number.isNaN(Number(e)),A=e=>!!e&&Number.isInteger(Number(e)),L=e=>e.endsWith("%")&&R(e.slice(0,-1)),P=e=>E.test(e),M=()=>!0,_=e=>N.test(e)&&!C.test(e),O=()=>!1,D=e=>z.test(e),I=e=>T.test(e),W=e=>!$(e)&&!Y(e),U=e=>ee(e,en,O),$=e=>x.test(e),G=e=>ee(e,el,_),H=e=>ee(e,ea,R),V=e=>ee(e,et,O),F=e=>ee(e,eo,I),X=e=>ee(e,es,D),Y=e=>k.test(e),B=e=>er(e,el),q=e=>er(e,ei),Z=e=>er(e,et),K=e=>er(e,en),Q=e=>er(e,eo),J=e=>er(e,es,!0),ee=(e,r,t)=>{let o=x.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},er=(e,r,t=!1)=>{let o=k.exec(e);return!!o&&(o[1]?r(o[1]):t)},et=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,el=e=>"length"===e,ea=e=>"number"===e,ei=e=>"family-name"===e,es=e=>"shadow"===e;Symbol.toStringTag;let ed=function(e,...r){let t,o,n,l=function(i){return o=(t=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=t.cache.set,l=a,a(i)};function a(e){let r=o(e);if(r)return r;let l=h(e,t);return n(e,l),l}return function(){return l(v.apply(null,arguments))}}(()=>{let e=y("color"),r=y("font"),t=y("text"),o=y("font-weight"),n=y("tracking"),l=y("leading"),a=y("breakpoint"),i=y("container"),s=y("spacing"),d=y("radius"),c=y("shadow"),u=y("inset-shadow"),p=y("text-shadow"),m=y("drop-shadow"),f=y("blur"),b=y("perspective"),g=y("aspect"),h=y("ease"),v=y("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],x=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...x(),Y,$],S=()=>["auto","hidden","clip","visible","scroll"],E=()=>["auto","contain","none"],N=()=>[Y,$,s],C=()=>[j,"full","auto",...N()],z=()=>[A,"none","subgrid",Y,$],T=()=>["auto",{span:["full",A,Y,$]},A,Y,$],_=()=>[A,"auto",Y,$],O=()=>["auto","min","max","fr",Y,$],D=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],I=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...N()],er=()=>[j,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...N()],et=()=>[e,Y,$],eo=()=>[...x(),Z,V,{position:[Y,$]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],el=()=>["auto","cover","contain",K,U,{size:[Y,$]}],ea=()=>[L,B,G],ei=()=>["","none","full",d,Y,$],es=()=>["",R,B,G],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[R,L,Z,V],ep=()=>["","none",f,Y,$],em=()=>["none",R,Y,$],ef=()=>["none",R,Y,$],eb=()=>[R,Y,$],eg=()=>[j,"full",...N()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[P],breakpoint:[P],color:[M],container:[P],"drop-shadow":[P],ease:["in","out","in-out"],font:[W],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[P],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[P],shadow:[P],spacing:["px",R],text:[P],"text-shadow":[P],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",j,$,Y,g]}],container:["container"],columns:[{columns:[R,$,Y,i]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:E()}],"overscroll-x":[{"overscroll-x":E()}],"overscroll-y":[{"overscroll-y":E()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:C()}],"inset-x":[{"inset-x":C()}],"inset-y":[{"inset-y":C()}],start:[{start:C()}],end:[{end:C()}],top:[{top:C()}],right:[{right:C()}],bottom:[{bottom:C()}],left:[{left:C()}],visibility:["visible","invisible","collapse"],z:[{z:[A,"auto",Y,$]}],basis:[{basis:[j,"full","auto",i,...N()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[R,j,"auto","initial","none",$]}],grow:[{grow:["",R,Y,$]}],shrink:[{shrink:["",R,Y,$]}],order:[{order:[A,"first","last","none",Y,$]}],"grid-cols":[{"grid-cols":z()}],"col-start-end":[{col:T()}],"col-start":[{"col-start":_()}],"col-end":[{"col-end":_()}],"grid-rows":[{"grid-rows":z()}],"row-start-end":[{row:T()}],"row-start":[{"row-start":_()}],"row-end":[{"row-end":_()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":O()}],"auto-rows":[{"auto-rows":O()}],gap:[{gap:N()}],"gap-x":[{"gap-x":N()}],"gap-y":[{"gap-y":N()}],"justify-content":[{justify:[...D(),"normal"]}],"justify-items":[{"justify-items":[...I(),"normal"]}],"justify-self":[{"justify-self":["auto",...I()]}],"align-content":[{content:["normal",...D()]}],"align-items":[{items:[...I(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...I(),{baseline:["","last"]}]}],"place-content":[{"place-content":D()}],"place-items":[{"place-items":[...I(),"baseline"]}],"place-self":[{"place-self":["auto",...I()]}],p:[{p:N()}],px:[{px:N()}],py:[{py:N()}],ps:[{ps:N()}],pe:[{pe:N()}],pt:[{pt:N()}],pr:[{pr:N()}],pb:[{pb:N()}],pl:[{pl:N()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":N()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":N()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[i,"screen",...er()]}],"min-w":[{"min-w":[i,"screen","none",...er()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[a]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,B,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,Y,H]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",L,$]}],"font-family":[{font:[q,$,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,Y,$]}],"line-clamp":[{"line-clamp":[R,"none",Y,H]}],leading:[{leading:[l,...N()]}],"list-image":[{"list-image":["none",Y,$]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,$]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[R,"from-font","auto",Y,G]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[R,"auto",Y,$]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:N()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,$]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,$]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:el()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},A,Y,$],radial:["",Y,$],conic:[A,Y,$]},Q,F]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:ea()}],"gradient-via-pos":[{via:ea()}],"gradient-to-pos":[{to:ea()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[R,Y,$]}],"outline-w":[{outline:["",R,B,G]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",c,J,X]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",u,J,X]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[R,G]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",p,J,X]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[R,Y,$]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[R]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[Y,$]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":x()}],"mask-image-conic-pos":[{"mask-conic":[R]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:el()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,$]}],filter:[{filter:["","none",Y,$]}],blur:[{blur:ep()}],brightness:[{brightness:[R,Y,$]}],contrast:[{contrast:[R,Y,$]}],"drop-shadow":[{"drop-shadow":["","none",m,J,X]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",R,Y,$]}],"hue-rotate":[{"hue-rotate":[R,Y,$]}],invert:[{invert:["",R,Y,$]}],saturate:[{saturate:[R,Y,$]}],sepia:[{sepia:["",R,Y,$]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,$]}],"backdrop-blur":[{"backdrop-blur":ep()}],"backdrop-brightness":[{"backdrop-brightness":[R,Y,$]}],"backdrop-contrast":[{"backdrop-contrast":[R,Y,$]}],"backdrop-grayscale":[{"backdrop-grayscale":["",R,Y,$]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[R,Y,$]}],"backdrop-invert":[{"backdrop-invert":["",R,Y,$]}],"backdrop-opacity":[{"backdrop-opacity":[R,Y,$]}],"backdrop-saturate":[{"backdrop-saturate":[R,Y,$]}],"backdrop-sepia":[{"backdrop-sepia":["",R,Y,$]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":N()}],"border-spacing-x":[{"border-spacing-x":N()}],"border-spacing-y":[{"border-spacing-y":N()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,$]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[R,"initial",Y,$]}],ease:[{ease:["linear","initial",h,Y,$]}],delay:[{delay:[R,Y,$]}],animate:[{animate:["none",v,Y,$]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,Y,$]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:em()}],"rotate-x":[{"rotate-x":em()}],"rotate-y":[{"rotate-y":em()}],"rotate-z":[{"rotate-z":em()}],scale:[{scale:ef()}],"scale-x":[{"scale-x":ef()}],"scale-y":[{"scale-y":ef()}],"scale-z":[{"scale-z":ef()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[Y,$,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,$]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":N()}],"scroll-mx":[{"scroll-mx":N()}],"scroll-my":[{"scroll-my":N()}],"scroll-ms":[{"scroll-ms":N()}],"scroll-me":[{"scroll-me":N()}],"scroll-mt":[{"scroll-mt":N()}],"scroll-mr":[{"scroll-mr":N()}],"scroll-mb":[{"scroll-mb":N()}],"scroll-ml":[{"scroll-ml":N()}],"scroll-p":[{"scroll-p":N()}],"scroll-px":[{"scroll-px":N()}],"scroll-py":[{"scroll-py":N()}],"scroll-ps":[{"scroll-ps":N()}],"scroll-pe":[{"scroll-pe":N()}],"scroll-pt":[{"scroll-pt":N()}],"scroll-pr":[{"scroll-pr":N()}],"scroll-pb":[{"scroll-pb":N()}],"scroll-pl":[{"scroll-pl":N()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,$]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[R,B,G,H]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,r,t)=>{t.d(r,{DX:()=>i,TL:()=>a});var o=t(2115),n=t(6101),l=t(5155);function a(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...l}=e;if(o.isValidElement(t)){var a;let e,i,s=(a=t,(i=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(i=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,r){let t={...r};for(let o in r){let n=e[o],l=r[o];/^on[A-Z]/.test(o)?n&&l?t[o]=(...e)=>{let r=l(...e);return n(...e),r}:n&&(t[o]=n):"style"===o?t[o]={...n,...l}:"className"===o&&(t[o]=[n,l].filter(Boolean).join(" "))}return{...e,...t}}(l,t.props);return t.type!==o.Fragment&&(d.ref=r?(0,n.t)(r,s):s),o.cloneElement(t,d)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:n,...a}=e,i=o.Children.toArray(n),s=i.find(d);if(s){let e=s.props.children,n=i.map(r=>r!==s?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,l.jsx)(r,{...a,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,l.jsx)(r,{...a,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var i=a("Slot"),s=Symbol("radix.slottable");function d(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},9946:(e,r,t)=>{t.d(r,{A:()=>u});var o=t(2115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),a=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},i=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},s=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,o.forwardRef)((e,r)=>{let{color:t="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:a,className:c="",children:u,iconNode:p,...m}=e;return(0,o.createElement)("svg",{ref:r,...d,width:n,height:n,stroke:t,strokeWidth:a?24*Number(l)/Number(n):l,className:i("lucide",c),...!u&&!s(m)&&{"aria-hidden":"true"},...m},[...p.map(e=>{let[r,t]=e;return(0,o.createElement)(r,t)}),...Array.isArray(u)?u:[u]])}),u=(e,r)=>{let t=(0,o.forwardRef)((t,l)=>{let{className:s,...d}=t;return(0,o.createElement)(c,{ref:l,iconNode:r,className:i("lucide-".concat(n(a(e))),"lucide-".concat(e),s),...d})});return t.displayName=a(e),t}}}]);