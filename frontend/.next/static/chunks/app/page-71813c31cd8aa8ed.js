(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5740:(e,t,r)=>{Promise.resolve().then(r.bind(r,8446))},8446:(e,t,r)=>{"use strict";r.d(t,{ChatInterface:()=>P});var s=r(5155),a=r(2115);let n=r(9509).env.NEXT_PUBLIC_API_URL||"http://localhost:5005";class i{static async sendMessage(e){try{let t=await fetch("".concat(n,"/api/rag"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error("HTTP error! status: ".concat(t.status));return await t.json()}catch(e){throw console.error("Error sending message:",e),Error("Failed to send message. Please try again.")}}}var l=r(4011),o=r(2596),c=r(9688);function d(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,c.QP)((0,o.$)(t))}function u(e){let{className:t,...r}=e;return(0,s.jsx)(l.bL,{"data-slot":"avatar",className:d("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...r})}function x(e){let{className:t,...r}=e;return(0,s.jsx)(l.H4,{"data-slot":"avatar-fallback",className:d("bg-muted flex size-full items-center justify-center rounded-full",t),...r})}function f(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:d("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function g(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:d("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function h(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:d("leading-none font-semibold",t),...r})}function m(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:d("px-6",t),...r})}var b=r(5657),p=r(1007);function v(e){let{message:t}=e,r="user"===t.role;return(0,s.jsxs)("div",{className:"flex gap-3 ".concat(r?"justify-end":"justify-start"," mb-4"),children:[!r&&(0,s.jsx)(u,{className:"w-8 h-8",children:(0,s.jsx)(x,{className:"bg-blue-500 text-white",children:(0,s.jsx)(b.A,{size:16})})}),(0,s.jsx)(f,{className:"max-w-[80%] ".concat(r?"bg-blue-500 text-white":"bg-gray-100"),children:(0,s.jsxs)(m,{className:"p-3",children:[(0,s.jsx)("div",{className:"text-sm whitespace-pre-wrap",children:t.content}),(0,s.jsx)("div",{className:"text-xs mt-2 opacity-70 ".concat(r?"text-blue-100":"text-gray-500"),children:t.timestamp.toLocaleTimeString()})]})}),r&&(0,s.jsx)(u,{className:"w-8 h-8",children:(0,s.jsx)(x,{className:"bg-gray-500 text-white",children:(0,s.jsx)(p.A,{size:16})})})]})}var j=r(9708);let y=(0,r(2085).F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function w(e){let{className:t,variant:r,size:a,asChild:n=!1,...i}=e,l=n?j.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:d(y({variant:r,size:a,className:t})),...i})}function N(e){let{className:t,type:r,...a}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:d("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...a})}var k=r(2486);function z(e){let{onSendMessage:t,disabled:r=!1}=e,[n,i]=(0,a.useState)(""),l=e=>{e.preventDefault(),n.trim()&&!r&&(t(n.trim()),i(""))};return(0,s.jsxs)("form",{onSubmit:l,className:"flex gap-2 p-4 border-t bg-white",children:[(0,s.jsx)(N,{value:n,onChange:e=>i(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),l(e))},placeholder:"Type your message...",disabled:r,className:"flex-1"}),(0,s.jsx)(w,{type:"submit",disabled:!n.trim()||r,size:"icon",children:(0,s.jsx)(k.A,{size:16})})]})}var S=r(7926);function _(e){let{className:t,children:r,...a}=e;return(0,s.jsxs)(S.bL,{"data-slot":"scroll-area",className:d("relative",t),...a,children:[(0,s.jsx)(S.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:r}),(0,s.jsx)(A,{}),(0,s.jsx)(S.OK,{})]})}function A(e){let{className:t,orientation:r="vertical",...a}=e;return(0,s.jsx)(S.VM,{"data-slot":"scroll-area-scrollbar",orientation:r,className:d("flex touch-none p-px transition-colors select-none","vertical"===r&&"h-full w-2.5 border-l border-l-transparent","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent",t),...a,children:(0,s.jsx)(S.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}var D=r(1366),E=r(1154);function P(){let[e,t]=(0,a.useState)([]),[r,n]=(0,a.useState)(!1),l=(0,a.useRef)(null);(0,a.useEffect)(()=>{if(l.current){let e=l.current.querySelector("[data-radix-scroll-area-viewport]");e&&(e.scrollTop=e.scrollHeight)}},[e]);let o=async r=>{let s={id:Date.now().toString(),role:"user",content:r,timestamp:new Date};t(e=>[...e,s]),n(!0);try{let s=e.map(e=>({role:e.role,content:e.content})),a=await i.sendMessage({question:r,history:s}),n={id:(Date.now()+1).toString(),role:"assistant",content:a.answer,timestamp:new Date};t(e=>[...e,n])}catch(r){let e={id:(Date.now()+1).toString(),role:"assistant",content:"Sorry, I encountered an error while processing your request. Please try again.",timestamp:new Date};t(t=>[...t,e]),console.error("Chat error:",r)}finally{n(!1)}};return(0,s.jsxs)(f,{className:"w-full max-w-4xl mx-auto h-[600px] flex flex-col",children:[(0,s.jsx)(g,{className:"border-b",children:(0,s.jsxs)(h,{className:"flex items-center gap-2",children:[(0,s.jsx)(D.A,{size:20}),"RAG Chat Assistant"]})}),(0,s.jsxs)(m,{className:"flex-1 p-0 flex flex-col",children:[(0,s.jsx)(_,{className:"flex-1 p-4",ref:l,children:0===e.length?(0,s.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(D.A,{size:48,className:"mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"Start a conversation by asking a question!"})]})}):(0,s.jsxs)("div",{className:"space-y-4",children:[e.map(e=>(0,s.jsx)(v,{message:e},e.id)),r&&(0,s.jsx)("div",{className:"flex justify-start mb-4",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-gray-100 rounded-lg p-3",children:[(0,s.jsx)(E.A,{size:16,className:"animate-spin"}),(0,s.jsx)("span",{className:"text-sm text-gray-600",children:"Thinking..."})]})})]})}),(0,s.jsx)(z,{onSendMessage:o,disabled:r})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[521,441,684,358],()=>t(5740)),_N_E=e.O()}]);