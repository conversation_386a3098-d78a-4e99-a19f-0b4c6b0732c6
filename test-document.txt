This is a test document for the RAG system.

The RAG (Retrieval Augmented Generation) system combines document retrieval with language generation to provide intelligent responses based on your documents.

Key features of this system:
1. Document processing for PDF, DOCX, and TXT files
2. Vector embeddings using OpenAI models
3. ChromaDB for efficient vector storage and retrieval
4. Google Gemini for intelligent response generation
5. Conversation history support for better context
6. Multi-language support
7. Beautiful chat interface built with Next.js and Tailwind CSS

The system can answer questions about your documents and maintain conversation context for better responses.
