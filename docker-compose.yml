
version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5005:5005"
    volumes:
      - ./input_files:/app/input_files
    environment:
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      GOOGLE_API_KEY: ${GOOGLE_API_KEY}
      CHROMA_DB_URL: http://chroma:8000
    depends_on:
      - chroma
    networks:
      - rag-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3001:3000"
    environment:
      NEXT_PUBLIC_API_URL: http://localhost:5005
    depends_on:
      - backend
    networks:
      - rag-network

  chroma:
    image: chromadb/chroma
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/app/chroma_data
    networks:
      - rag-network

volumes:
  chroma_data:

networks:
  rag-network:
    driver: bridge


