import express from "express";
import cors from "cors";
import dotenv from "dotenv";
import processRoute from "./routes/process";
import queryRoute from "./routes/query";
import ragRoute from "./routes/rag";

dotenv.config();

const app = express();
const port = process.env.PORT || 5005;

// Enable CORS for all routes
app.use(
  cors({
    origin: process.env.FRONTEND_URL || "http://localhost:3001",
    credentials: true,
  })
);

app.use(express.json());
app.use(express.urlencoded({ extended: true })); // For parsing application/x-www-form-urlencoded

app.use("/api", processRoute);
app.use("/api", queryRoute);
app.use("/api", ragRoute);

app.get("/", (req, res) => {
  res.send("RAG Project API is running!");
});

app.listen(port, () => {
  console.log(`Server running on http://localhost:${port}`);
});
