import { Router } from "express";
import { retrieveDocuments } from "../services/langchainService";
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";

const router = Router();

// Helper function to reformulate question with history context
const reformulateQuestion = async (
  question: string,
  history: Array<{ role: string; content: string }>
): Promise<string> => {
  if (!history || history.length === 0) {
    return question;
  }

  const chat = new ChatGoogleGenerativeAI({
    model: "gemini-2.0-flash",
    temperature: 0.3,
    apiKey: process.env.GOOGLE_API_KEY,
  });

  const historyContext = history
    .map((msg) => `${msg.role}: ${msg.content}`)
    .join("\n");

  const reformulationPrompt = `Given the conversation history below, reformulate the user's current question to be more specific and contextual for better document retrieval. The reformulated question should be standalone and include relevant context from the conversation history.

Conversation History:
${historyContext}

Current Question: ${question}

Reformulated Question:`;

  try {
    const response = await chat.invoke([
      {
        role: "user",
        content: reformulationPrompt,
      },
    ]);

    return response.content as string;
  } catch (error) {
    console.error("Error reformulating question:", error);
    return question; // Fallback to original question
  }
};

router.post("/rag", async (req, res) => {
  const { question, history = [] } = req.body;

  if (!question) {
    return res.status(400).send("Question is required.");
  }

  try {
    const collectionName = "mwanrag"; // Should match the collection name used in process.ts

    // Reformulate question with history context for better retrieval
    const reformulatedQuestion = await reformulateQuestion(question, history);
    console.log("Original question:", question);
    console.log("Reformulated question:", reformulatedQuestion);

    const relevantDocs = await retrieveDocuments(
      reformulatedQuestion,
      collectionName
    );
    log
    const context = relevantDocs.map((doc) => doc.pageContent).join("\n\n");

    const chat = new ChatGoogleGenerativeAI({
      model: "gemini-2.0-flash-exp",
      temperature: 0.7,
      apiKey: process.env.GOOGLE_API_KEY,
    });

    // Build conversation messages including history
    const messages = [
      {
        role: "system",
        content: `You are a helpful AI assistant. Use the following context to answer the user's question. Be aware of the conversation history and respond in the same language the user is using. Provide accurate, helpful responses based on the context provided.

Context from documents:
${context}`,
      },
    ];

    // Add conversation history
    if (history && history.length > 0) {
      history.forEach((msg: { role: string; content: string }) => {
        messages.push({
          role: msg.role === "user" ? "user" : "assistant",
          content: msg.content,
        });
      });
    }

    // Add current question
    messages.push({
      role: "user",
      content: question,
    });

    const response = await chat.invoke(messages);

    res.status(200).json({
      answer: response.content,
      reformulatedQuestion:
        reformulatedQuestion !== question ? reformulatedQuestion : undefined,
    });
  } catch (error) {
    console.error("Error in RAG endpoint:", error);
    res.status(500).send("Error processing RAG request.");
  }
});

export default router;
