{"name": "@langchain/google-genai", "version": "0.0.26", "description": "Google Generative AI integration for LangChain.js", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "**************:langchain-ai/langchainjs.git"}, "homepage": "https://github.com/langchain-ai/langchainjs/tree/main/libs/langchain-google-genai/", "scripts": {"build": "yarn turbo:command build:internal --filter=@langchain/google-genai", "build:internal": "yarn lc_build_v2 --create-entrypoints --pre --tree-shaking", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "clean": "rm -rf .turbo dist/", "prepack": "yarn build", "test": "NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "test:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard:unit": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.standard\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.standard\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard": "yarn test:standard:unit && yarn test:standard:int", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\""}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@google/generative-ai": "^0.7.0", "@langchain/core": ">=0.2.21 <0.3.0", "zod-to-json-schema": "^3.22.4"}, "devDependencies": {"@jest/globals": "^29.5.0", "@langchain/scripts": "~0.0.20", "@langchain/standard-tests": "0.0.0", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "@tsconfig/recommended": "^1.0.3", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "dotenv": "^16.3.1", "dpdm": "^3.12.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "hnswlib-node": "^3.0.0", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "prettier": "^2.8.3", "release-it": "^17.6.0", "rollup": "^4.5.2", "ts-jest": "^29.1.0", "typescript": "<5.2.0", "zod": "^3.22.4"}, "publishConfig": {"access": "public"}, "exports": {".": {"types": {"import": "./index.d.ts", "require": "./index.d.cts", "default": "./index.d.ts"}, "import": "./index.js", "require": "./index.cjs"}, "./package.json": "./package.json"}, "files": ["dist/", "index.cjs", "index.js", "index.d.ts", "index.d.cts"]}